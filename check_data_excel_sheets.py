import pandas as pd

# 读取data目录下的Excel文件并检查工作表
xl = pd.ExcelFile('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx')

print('所有工作表名称:')
for i, sheet_name in enumerate(xl.sheet_names):
    print(f'{i+1}. {sheet_name}')

print('\n检查是否存在方向和体位工作表:')
print('方向工作表存在:', '方向' in xl.sheet_names)
print('体位工作表存在:', '体位' in xl.sheet_names)

# 显示所有工作表的基本信息
for sheet_name in xl.sheet_names:
    print(f'\n=== {sheet_name} 工作表内容 ===')
    try:
        df = pd.read_excel('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx', sheet_name=sheet_name)
        print(f'行数: {len(df)}, 列数: {len(df.columns)}')
        print('列名:', list(df.columns))
        print('前3行数据:')
        print(df.head(3))
    except Exception as e:
        print(f'读取失败: {e}')

print('\n检查完成！')