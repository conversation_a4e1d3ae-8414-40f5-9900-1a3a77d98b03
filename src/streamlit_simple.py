#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医学检查项目处理流程 - 简化版Streamlit应用
专注于核心功能，避免复杂图表问题
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime
import re
import os
from io import BytesIO

# 导入DR处理器
try:
    from dr_processor import DRProcessor
except ImportError:
    DRProcessor = None

def get_project_paths():
    """获取项目路径配置"""
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 项目根目录（src的上级目录）
    project_root = os.path.dirname(current_dir)

    # 数据目录和输出目录
    data_dir = os.path.join(project_root, "data")
    output_dir = os.path.join(project_root, "output")

    return {
        'project_root': project_root,
        'data_dir': data_dir,
        'output_dir': output_dir,
        'default_data_file': os.path.join(data_dir, "NEW_检查项目名称结构表 (8).xlsx")
    }

# 设置页面配置
st.set_page_config(
    page_title="医学检查项目处理流程",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

class SimpleMedicalProcessor:
    """简化版医学检查项目处理器"""

    def __init__(self):
        self.main_df = None
        self.ct_scan_df = None
        self.mr_scan_df = None
        # DR相关数据
        self.dr_df = None
        self.dr_direction_df = None
        self.dr_position_df = None

    def load_data(self, uploaded_file):
        """加载Excel数据"""
        try:
            # 检查文件中的工作表
            xl = pd.ExcelFile(uploaded_file)
            sheet_names = xl.sheet_names

            # 加载CT/MR数据（原有逻辑）
            if '三级部位结构' in sheet_names:
                self.main_df = pd.read_excel(uploaded_file, sheet_name='三级部位结构')
            if 'CT扫描方式' in sheet_names:
                self.ct_scan_df = pd.read_excel(uploaded_file, sheet_name='CT扫描方式')
            if 'MR扫描方式' in sheet_names:
                self.mr_scan_df = pd.read_excel(uploaded_file, sheet_name='MR扫描方式')

            # 加载DR数据（新增逻辑）
            if 'DR' in sheet_names:
                self.dr_df = pd.read_excel(uploaded_file, sheet_name='DR')
            if '方向' in sheet_names:
                self.dr_direction_df = pd.read_excel(uploaded_file, sheet_name='方向')
            if '体位' in sheet_names:
                self.dr_position_df = pd.read_excel(uploaded_file, sheet_name='体位')

            # 确定数据类型
            data_types = []
            if self.main_df is not None:
                data_types.append("CT/MR部位数据")
            if self.dr_df is not None:
                data_types.append("DR检查数据")

            if not data_types:
                return False, "未找到有效的数据工作表"

            return True, f"数据加载成功：{', '.join(data_types)}"
        except Exception as e:
            return False, f"数据加载失败：{str(e)}"

    def get_basic_stats(self):
        """获取基本统计信息"""
        stats = {}

        # CT/MR数据统计
        if self.main_df is not None:
            stats.update({
                'main_rows': len(self.main_df),
                'main_cols': len(self.main_df.columns),
                'missing_values': int(self.main_df.isnull().sum().sum()),
                'ct_scans': len(self.ct_scan_df) if self.ct_scan_df is not None else 0,
                'mr_scans': len(self.mr_scan_df) if self.mr_scan_df is not None else 0,
                'ct_parts': len(self.main_df[self.main_df['CT'].astype(str).str.strip().isin(['1', '１'])]) if 'CT' in self.main_df.columns else 0,
                'mr_parts': len(self.main_df[self.main_df['MR'].astype(str).str.strip().isin(['1', '１'])]) if 'MR' in self.main_df.columns else 0,
                'dr_applicable_parts': len(self.main_df[self.main_df['DR'].astype(str).str.strip().isin(['1', '１'])]) if 'DR' in self.main_df.columns else 0
            })

        # DR数据统计
        if self.dr_df is not None:
            stats.update({
                'dr_rows': len(self.dr_df),
                'dr_cols': len(self.dr_df.columns),
                'dr_missing_values': int(self.dr_df.isnull().sum().sum()),
                'dr_directions': len(self.dr_direction_df) if self.dr_direction_df is not None else 0,
                'dr_positions': len(self.dr_position_df) if self.dr_position_df is not None else 0,
                'dr_valid_projects': len(self.dr_df[self.dr_df['项目名称'].notna()]) if '项目名称' in self.dr_df.columns else 0
            })

        return stats if stats else None

    def clean_scan_name(self, scan_name, modality):
        """清理扫描方式名称"""
        if pd.isna(scan_name):
            return ""

        scan_name = str(scan_name).strip()

        if modality == 'CT':
            # 只去除前缀，保留CTA、CTV等完整名称
            if scan_name.startswith('CT-'):
                scan_name = scan_name[3:]
            elif scan_name.startswith('CT_'):
                scan_name = scan_name[3:]
            elif scan_name.startswith('CT') and not scan_name.startswith('CTA') and not scan_name.startswith('CTV') and not scan_name.startswith('CTU'):
                scan_name = scan_name[2:]
        elif modality == 'MR':
            # 只去除前缀，保留MRA、MRV等完整名称
            if scan_name.startswith('MR-'):
                scan_name = scan_name[3:]
            elif scan_name.startswith('MR_'):
                scan_name = scan_name[3:]
            elif scan_name.startswith('MR') and not scan_name.startswith('MRA') and not scan_name.startswith('MRV') and not scan_name.startswith('MRH') and not scan_name.startswith('MRM') and not scan_name.startswith('MRCP') and not scan_name.startswith('MRU'):
                scan_name = scan_name[2:]

        return re.sub(r'\s+', ' ', scan_name).strip()

    def generate_three_level_dict(self):
        """生成三级部位字典表"""
        if self.main_df is None:
            return None

        dict_data = []
        for _, row in self.main_df.iterrows():
            part_code = str(row.get('部位编码', '')).strip()
            if len(part_code) == 6 and part_code.isdigit():
                dict_item = {
                    '一级编码': row.get('一级编码', ''),
                    '一级部位': str(row.get('一级部位', '')).strip(),
                    '二级编码': row.get('二级编码', ''),
                    '二级部位': str(row.get('二级部位', '')).strip(),  # 修复：使用正确的列名
                    '三级编码': row.get('三级编码', ''),
                    '三级部位': str(row.get('三级部位', '')).strip(),
                    '部位编码': part_code,
                    'CT适用': '是' if str(row.get('CT', '')).strip() in ['1', '１'] else '否',
                    'MR适用': '是' if str(row.get('MR', '')).strip() in ['1', '１'] else '否',
                    'DR适用': '是' if str(row.get('DR', '')).strip() in ['1', '１'] else '否'
                }
                dict_data.append(dict_item)

        return pd.DataFrame(dict_data)

    def analyze_duplicate_codes(self, three_level_dict):
        """分析重复的部位编码"""
        if three_level_dict is None or len(three_level_dict) == 0:
            return None

        # 找出重复的编码
        code_counts = three_level_dict['部位编码'].value_counts()
        duplicate_codes = code_counts[code_counts > 1]

        if len(duplicate_codes) == 0:
            return None

        # 详细分析每个重复编码
        duplicate_analysis = []
        for code, count in duplicate_codes.items():
            duplicate_rows = three_level_dict[three_level_dict['部位编码'] == code]

            for idx, (_, row) in enumerate(duplicate_rows.iterrows()):
                duplicate_analysis.append({
                    '部位编码': code,
                    '重复次数': count,
                    '序号': idx + 1,
                    '一级部位': row['一级部位'],
                    '二级部位': row['二级部位'],
                    '三级部位': row['三级部位'],
                    'CT适用': row['CT适用'],
                    'MR适用': row['MR适用']
                })

        return pd.DataFrame(duplicate_analysis)

    def get_duplicate_statistics(self, duplicate_analysis):
        """获取重复编码统计信息"""
        if duplicate_analysis is None:
            return None

        stats = {
            'total_duplicates': len(duplicate_analysis['部位编码'].unique()),
            'total_affected_records': len(duplicate_analysis),
            'by_level1': duplicate_analysis.groupby('一级部位')['部位编码'].nunique().to_dict(),
            'by_level2': duplicate_analysis.groupby('二级部位')['部位编码'].nunique().to_dict(),
            'max_duplicates': duplicate_analysis['重复次数'].max(),
            'duplicate_codes': duplicate_analysis['部位编码'].unique().tolist()
        }

        return stats

    def generate_scan_dict(self, modality):
        """生成扫描方式字典表 - 保留原始sheet表中的所有字段"""
        if modality == 'CT' and self.ct_scan_df is not None:
            # 复制原始数据框，保留所有字段
            scan_dict = self.ct_scan_df.copy()

            # 过滤掉无效数据
            scan_dict = scan_dict.dropna(subset=['CT扫描名称', 'CT扫描编码'])

            # 添加清理后名称字段
            scan_dict['清理后名称'] = scan_dict['CT扫描名称'].apply(
                lambda x: self.clean_scan_name(x, 'CT') if pd.notna(x) else ''
            )

            # 确保编码格式正确
            scan_dict['CT扫描编码'] = scan_dict['CT扫描编码'].apply(
                lambda x: str(x).zfill(2) if pd.notna(x) else ''
            )

            # 重置索引
            scan_dict = scan_dict.reset_index(drop=True)

            return scan_dict

        elif modality == 'MR' and self.mr_scan_df is not None:
            # 复制原始数据框，保留所有字段
            scan_dict = self.mr_scan_df.copy()

            # 过滤掉无效数据
            scan_dict = scan_dict.dropna(subset=['MR成像名称', 'MR成像编码'])

            # 添加清理后名称字段
            scan_dict['清理后名称'] = scan_dict['MR成像名称'].apply(
                lambda x: self.clean_scan_name(x, 'MR') if pd.notna(x) else ''
            )

            # 确保编码格式正确
            scan_dict['MR成像编码'] = scan_dict['MR成像编码'].apply(
                lambda x: str(x).zfill(2) if pd.notna(x) else ''
            )

            # 重置索引
            scan_dict = scan_dict.reset_index(drop=True)

            return scan_dict

        return None

    def generate_check_items(self, modality):
        """生成检查项目"""
        if self.main_df is None:
            return None

        items = []
        mapping = self.create_scan_mapping(modality)

        # 获取相关列
        if modality == 'CT':
            scan_columns = [col for col in self.main_df.columns if col.startswith('CT') and col != 'CT']
        else:
            scan_columns = [col for col in self.main_df.columns if col.startswith('MR') and col != 'MR']

        for _, row in self.main_df.iterrows():
            if pd.notna(row.get(modality)) and str(row.get(modality)).strip() in ['1', '１']:
                part_name = str(row['三级部位']).strip()
                part_code = str(row['部位编码']).strip()

                for scan_col in scan_columns:
                    if pd.notna(row[scan_col]) and row[scan_col] == 1.0:
                        scan_raw_name = scan_col
                        scan_clean_name = self.clean_scan_name(scan_raw_name, modality)
                        scan_code = self.find_scan_code(scan_clean_name, mapping, modality)

                        item_name = f"{modality}{part_name}({scan_clean_name})"
                        item_code = f"{modality}{part_code}{scan_code}".replace(' ', '')

                        item = {
                            '模态': modality,
                            '一级编码': row.get('一级编码', ''),
                            '一级部位': row['一级部位'],
                            '二级编码': row.get('二级编码', ''),
                            '二级部位': row.get('二级部位', ''),
                            '三级编码': row.get('三级编码', ''),
                            '三级部位': part_name,
                            '项目编码': item_code,
                            '项目名称': item_name,
                            # 保留原有列用于内部处理
                            '部位编码': part_code,
                            '扫描方式清理名': scan_clean_name,
                            '扫描编码': scan_code,
                            '检查模态': modality
                        }
                        items.append(item)

        items_df = pd.DataFrame(items)

        # 生成无部位项目并合并
        no_part_items = self.generate_no_part_items(modality)
        if no_part_items is not None and len(no_part_items) > 0:
            items_df = pd.concat([items_df, no_part_items], ignore_index=True)

        return items_df

    def generate_no_part_items(self, modality):
        """生成无部位项目"""
        items = []

        if modality == 'CT' and self.ct_scan_df is not None:
            for _, row in self.ct_scan_df.iterrows():
                # CT无部位项目生成规则
                scan_class_code = row.get('CT扫描分类编码', '')
                scan_code = row.get('CT扫描编码', '')
                scan_class_name = row.get('CT扫描分类名称', '')
                scan_name = row.get('CT扫描名称', '')

                # 检查数据有效性，跳过NaN值
                if (pd.notna(scan_class_code) and pd.notna(scan_code) and
                    pd.notna(scan_class_name) and pd.notna(scan_name) and
                    str(scan_class_code).strip() != '' and str(scan_code).strip() != '' and
                    str(scan_class_name).strip() != '' and str(scan_name).strip() != ''):

                    scan_class_code = str(scan_class_code).zfill(2)
                    scan_code = str(scan_code).zfill(2)
                    scan_class_name = str(scan_class_name).strip()
                    scan_name = str(scan_name).strip()
                    # 项目编码格式：CT + 09 + 二级编码 + 三级编码 + 00
                    item_code = f"CT09{scan_class_code}{scan_code}00"

                    item = {
                        '模态': 'CT',
                        '一级编码': '09',
                        '一级部位': '无指定部位',
                        '二级编码': scan_class_code,
                        '二级部位': scan_class_name,
                        '三级编码': scan_code,
                        '三级部位': scan_name,
                        '项目编码': item_code,
                        '项目名称': scan_name,
                        # 保留原有列用于内部处理
                        '部位编码': '090000',  # 无部位的特殊编码
                        '扫描方式清理名': '',
                        '扫描编码': '00',
                        '检查模态': 'CT',
                        # 保留扫描方式相关字段的完整性
                        'CT扫描分类编码': scan_class_code,
                        'CT扫描分类名称': scan_class_name,
                        'CT扫描编码': scan_code,
                        'CT扫描名称': scan_name
                    }
                    items.append(item)

        elif modality == 'MR' and self.mr_scan_df is not None:
            for _, row in self.mr_scan_df.iterrows():
                # MR无部位项目生成规则
                imaging_class_code = row.get('MR成像分类编码', '')
                imaging_code = row.get('MR成像编码', '')
                imaging_class_name = row.get('MR成像分类', '')
                imaging_name = row.get('MR成像名称', '')

                # 检查数据有效性，跳过NaN值
                if (pd.notna(imaging_class_code) and pd.notna(imaging_code) and
                    pd.notna(imaging_class_name) and pd.notna(imaging_name) and
                    str(imaging_class_code).strip() != '' and str(imaging_code).strip() != '' and
                    str(imaging_class_name).strip() != '' and str(imaging_name).strip() != ''):

                    imaging_class_code = str(imaging_class_code).zfill(2)
                    imaging_code = str(imaging_code).zfill(2)
                    imaging_class_name = str(imaging_class_name).strip()
                    imaging_name = str(imaging_name).strip()
                    # 项目编码格式：MR + 09 + 二级编码 + 三级编码 + 00
                    item_code = f"MR09{imaging_class_code}{imaging_code}00"

                    item = {
                        '模态': 'MR',
                        '一级编码': '09',
                        '一级部位': '无指定部位',
                        '二级编码': imaging_class_code,
                        '二级部位': imaging_class_name,
                        '三级编码': imaging_code,
                        '三级部位': imaging_name,
                        '项目编码': item_code,
                        '项目名称': imaging_name,
                        # 保留原有列用于内部处理
                        '部位编码': '090000',  # 无部位的特殊编码
                        '扫描方式清理名': '',
                        '扫描编码': '00',
                        '检查模态': 'MR',
                        # 保留扫描方式相关字段的完整性
                        'MR成像分类编码': imaging_class_code,
                        'MR成像分类': imaging_class_name,
                        'MR成像编码': imaging_code,
                        'MR成像名称': imaging_name
                    }
                    items.append(item)

        return pd.DataFrame(items) if items else None

    def format_output_columns(self, items_df):
        """格式化输出列顺序为标准格式"""
        if items_df is None or len(items_df) == 0:
            return items_df

        # 标准输出列顺序
        standard_columns = [
            '模态',
            '一级编码',
            '一级部位',
            '二级编码',
            '二级部位',
            '三级编码',
            '三级部位',
            '项目编码',
            '项目名称'
        ]

        # 确保所有标准列都存在
        for col in standard_columns:
            if col not in items_df.columns:
                items_df[col] = ''

        # 返回按标准顺序排列的DataFrame
        return items_df[standard_columns]

    def create_scan_mapping(self, modality):
        """创建扫描方式映射表"""
        mapping = {}
        priority_mapping = {}  # 优先级映射，用于标准编码

        if modality == 'CT' and self.ct_scan_df is not None:
            for _, row in self.ct_scan_df.iterrows():
                scan_name = row.get('CT扫描名称', '')
                scan_code = row.get('CT扫描编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    clean_name = self.clean_scan_name(scan_name, 'CT')
                    code_str = str(scan_code).zfill(2)

                    # 基本映射
                    mapping[scan_name] = code_str
                    mapping[clean_name] = code_str

                    # 优先级映射：标准编码优先于加收编码
                    is_standard = not ('加收' in scan_name or '心电门控' in scan_name or
                                     '薄层' in scan_name or '能量' in scan_name or '能谱' in scan_name)

                    if is_standard:
                        # 标准扫描方式的优先级映射
                        if '灌注成像' in scan_name and scan_name == 'CT灌注成像':
                            priority_mapping['灌注成像'] = code_str
                            priority_mapping['灌注'] = code_str
                        elif 'CTA' in scan_name:
                            priority_mapping['血管成像CTA'] = code_str
                            priority_mapping['CTA'] = code_str
                        elif 'CTV' in scan_name:
                            priority_mapping['血管成像CTV'] = code_str
                            priority_mapping['CTV'] = code_str
                        elif '平扫+增强' in scan_name and scan_name == 'CT平扫+增强':
                            priority_mapping['平扫+增强'] = code_str
                        elif '增强' in scan_name and scan_name == 'CT增强':
                            priority_mapping['增强'] = code_str
                        elif '平扫' in scan_name and scan_name == 'CT平扫':
                            priority_mapping['平扫'] = code_str

                    # 特殊处理：确保关键扫描方式有正确映射
                    if 'CTA' in scan_name:
                        mapping['血管成像CTA'] = code_str
                        mapping['CTA'] = code_str
                    if 'CTV' in scan_name:
                        mapping['血管成像CTV'] = code_str
                        mapping['CTV'] = code_str
                    if '灌注' in scan_name:
                        mapping['灌注成像'] = code_str
                        mapping['灌注'] = code_str

        elif modality == 'MR' and self.mr_scan_df is not None:
            for _, row in self.mr_scan_df.iterrows():
                scan_name = row.get('MR成像名称', '')
                scan_code = row.get('MR成像编码', '')
                if pd.notna(scan_name) and pd.notna(scan_code):
                    clean_name = self.clean_scan_name(scan_name, 'MR')
                    code_str = str(scan_code).zfill(2)

                    # 基本映射
                    mapping[scan_name] = code_str
                    mapping[clean_name] = code_str

                    # 特殊处理血管成像 - 保持原始编码，精确匹配避免覆盖
                    # 只为特定的扫描方式创建映射，避免复合扫描方式覆盖简单扫描方式
                    if scan_name == 'MR_血管平扫MRA':
                        mapping['血管成像MRA'] = code_str
                        mapping['MRA'] = code_str
                        mapping['血管平扫MRA'] = code_str
                    elif scan_name == 'MR_血管平扫MRV':
                        mapping['血管成像MRV'] = code_str
                        mapping['MRV'] = code_str
                        mapping['血管平扫MRV'] = code_str
                    elif scan_name == 'MR-血管增强CE_MRA':
                        mapping['血管增强成像CE_MRA'] = code_str
                        mapping['CE_MRA'] = code_str
                        mapping['血管增强CE_MRA'] = code_str
                    elif scan_name == 'MR-血管增强CE_MRV':
                        mapping['血管增强成像CE_MRV'] = code_str
                        mapping['CE_MRV'] = code_str
                        mapping['血管增强CE_MRV'] = code_str

                    # 其他特殊处理
                    if 'ASL' in scan_name:
                        mapping['平扫+灌注ASL'] = code_str
                        mapping['ASL'] = code_str
                    if 'DCE' in scan_name:
                        mapping['平扫+增强灌注DCE'] = code_str
                        mapping['DCE'] = code_str

        # 将优先级映射覆盖到基本映射中
        mapping.update(priority_mapping)

        return mapping

    def find_scan_code(self, scan_clean_name, mapping, modality):
        """查找扫描编码 - 优先使用字典表中的标准编码"""
        # 1. 直接精确匹配
        if scan_clean_name in mapping:
            return mapping[scan_clean_name]

        # 2. 优先匹配标准扫描方式（非加收项目）
        standard_matches = []
        addon_matches = []

        for key, code in mapping.items():
            if scan_clean_name in key or key in scan_clean_name:
                # 判断是否为标准扫描方式
                is_standard = not any(addon_keyword in key for addon_keyword in
                                    ['加收', '心电门控', '薄层', '能量', '能谱', '门控'])
                if is_standard:
                    standard_matches.append((key, code))
                else:
                    addon_matches.append((key, code))

        # 优先返回标准扫描方式的编码
        if standard_matches:
            # 如果有多个标准匹配，选择最精确的匹配
            best_match = min(standard_matches, key=lambda x: len(x[0]))
            return best_match[1]

        # 如果没有标准匹配，使用加收项目的编码
        if addon_matches:
            best_match = min(addon_matches, key=lambda x: len(x[0]))
            return best_match[1]

        # 3. 使用字典表中的默认规则（仅作为最后备选）
        if modality == 'CT':
            # 基于字典表的标准编码
            if '灌注' in scan_clean_name:
                return '50'  # CT灌注成像的标准编码
            elif 'CTA' in scan_clean_name or '血管成像CTA' in scan_clean_name:
                return '41'  # CT血管造影成像CTA - 保持原有编码
            elif 'CTV' in scan_clean_name or '血管成像CTV' in scan_clean_name:
                return '42'  # CT血管造影成像CTV - 保持原有编码
            elif 'CTU' in scan_clean_name or '延时显像' in scan_clean_name:
                return '23'  # CT延迟显像(扩展)CTU
            elif '平扫+增强' in scan_clean_name:
                return '30'  # CT平扫+增强
            elif '增强' in scan_clean_name:
                return '20'  # CT增强
            elif '平扫' in scan_clean_name:
                return '10'  # CT平扫
        else:
            # MR的默认规则 - 根据实际数据表修正血管造影类扫描方式编码
            # 优先匹配复合扫描方式
            if 'MRA+CE_MRA' in scan_clean_name or '血管平扫+增强MRA+CE_MRA' in scan_clean_name:
                return '61'  # MR-血管平扫+增强MRA+CE_MRA
            elif 'MRV+CE_MRV' in scan_clean_name or '血管平扫+增强MRV+CE_MRV' in scan_clean_name:
                return '62'  # MR-血管平扫+增强MRV+CE_MRV
            elif 'CE_MRA' in scan_clean_name or '血管增强成像CE_MRA' in scan_clean_name or '血管增强CE_MRA' in scan_clean_name:
                return '51'  # MR-血管增强CE_MRA
            elif 'CE_MRV' in scan_clean_name or '血管增强成像CE_MRV' in scan_clean_name or '血管增强CE_MRV' in scan_clean_name:
                return '52'  # MR-血管增强CE_MRV
            elif 'MRA' in scan_clean_name or '血管成像MRA' in scan_clean_name or '血管平扫MRA' in scan_clean_name:
                return '41'  # MR_血管平扫MRA
            elif 'MRV' in scan_clean_name or '血管成像MRV' in scan_clean_name or '血管平扫MRV' in scan_clean_name:
                return '42'  # MR_血管平扫MRV
            elif 'ASL' in scan_clean_name or '灌注ASL' in scan_clean_name:
                return '50'
            elif 'DCE' in scan_clean_name or '增强灌注DCE' in scan_clean_name:
                return '51'
            elif '电影成像' in scan_clean_name:
                return '60'
            elif 'MRCP' in scan_clean_name or '水成像MRCP' in scan_clean_name:
                return '70'
            elif 'MRU' in scan_clean_name or '水成像MRU' in scan_clean_name:
                return '71'
            elif 'MRH' in scan_clean_name or '水成像MRH' in scan_clean_name:
                return '72'
            elif 'MRM' in scan_clean_name or '水成像MRM' in scan_clean_name:
                return '73'
            elif 'IEHM' in scan_clean_name or '水成像IEHM' in scan_clean_name:
                return '74'
            elif '平扫+增强' in scan_clean_name:
                return '30'
            elif '增强' in scan_clean_name:
                return '20'
            elif '平扫' in scan_clean_name:
                return '10'

        # 4. 默认未知类型
        return '99'

    def generate_dr_items(self, use_standard_codes=True):
        """生成DR检查项目清单"""
        if self.dr_df is None or DRProcessor is None:
            return None, None

        # 创建DR处理器实例
        dr_processor = DRProcessor()

        # 设置数据
        dr_processor.dr_df = self.dr_df
        dr_processor.direction_df = self.dr_direction_df
        dr_processor.position_df = self.dr_position_df

        # 创建编码映射
        if self.dr_direction_df is not None:
            dr_processor.direction_mapping = dict(zip(
                self.dr_direction_df['方向'],
                self.dr_direction_df['方向编码']
            ))

        if self.dr_position_df is not None:
            dr_processor.position_mapping = dict(zip(
                self.dr_position_df['体位'],
                self.dr_position_df['体位编码']
            ))

        # 如果使用标准编码且有三级部位字典表
        if use_standard_codes and self.main_df is not None:
            # 生成三级部位字典表
            three_level_dict = self.generate_three_level_dict()
            if three_level_dict is not None:
                # 设置三级部位字典表
                dr_processor.set_three_level_dict(three_level_dict)

                # 执行部位映射
                success, message = dr_processor.map_dr_parts_to_standard()
                if not success:
                    return None, f"部位映射失败：{message}"

        # 生成DR项目清单
        dr_items = dr_processor.generate_dr_items()
        mapping_results = dr_processor.part_mapping_results

        return dr_items, mapping_results

    def get_dr_stats(self):
        """获取DR数据统计信息"""
        if self.dr_df is None:
            return None

        stats = {
            'total_records': len(self.dr_df),
            'valid_projects': len(self.dr_df[self.dr_df['项目名称'].notna()]),
            'level1_parts': len(self.dr_df['一级部位'].unique()) if '一级部位' in self.dr_df.columns else 0,
            'level2_parts': len(self.dr_df['二级部位'].unique()) if '二级部位' in self.dr_df.columns else 0,
            'level3_parts': len(self.dr_df['三级部位'].unique()) if '三级部位' in self.dr_df.columns else 0,
            'positions': len(self.dr_df['摆位'].unique()) if '摆位' in self.dr_df.columns else 0,
            'direction_codes': len(self.dr_direction_df) if self.dr_direction_df is not None else 0,
            'position_codes': len(self.dr_position_df) if self.dr_position_df is not None else 0
        }

        return stats

def main():
    """主函数"""
    st.title("🏥 医学检查项目名称和编码处理流程")
    st.markdown("---")

    # 初始化处理器
    if 'processor' not in st.session_state:
        st.session_state.processor = SimpleMedicalProcessor()

    # 侧边栏 - 文件上传
    st.sidebar.header("📁 数据源")

    # 获取路径配置并检查默认文件
    paths = get_project_paths()
    default_file = paths['default_data_file']

    if os.path.exists(default_file):
        if st.sidebar.button("使用默认数据文件"):
            success, message = st.session_state.processor.load_data(default_file)
            if success:
                st.sidebar.success(message)
                st.session_state.data_loaded = True
            else:
                st.sidebar.error(message)
    else:
        st.sidebar.info("默认数据文件不存在，请上传Excel文件")
        st.sidebar.caption(f"期望路径：{default_file}")

    # 文件上传
    uploaded_file = st.sidebar.file_uploader(
        "上传Excel文件",
        type=['xlsx', 'xls'],
        help="支持两种数据类型：\n1. CT/MR数据：包含'三级部位结构'、'CT扫描方式'、'MR扫描方式'工作表\n2. DR数据：包含'DR'、'方向'、'体位'工作表"
    )

    if uploaded_file is not None:
        success, message = st.session_state.processor.load_data(uploaded_file)
        if success:
            st.sidebar.success(message)
            st.session_state.data_loaded = True
        else:
            st.sidebar.error(message)
            st.session_state.data_loaded = False

    # 主界面
    if not hasattr(st.session_state, 'data_loaded') or not st.session_state.data_loaded:
        st.info("请在侧边栏上传Excel数据文件或使用默认文件开始处理")
        return

    processor = st.session_state.processor

    # 检测数据类型
    has_ct_mr_data = processor.main_df is not None
    has_dr_data = processor.dr_df is not None

    # 步骤选择
    st.sidebar.header("📋 处理步骤")

    # 根据数据类型显示不同的步骤选项
    if has_ct_mr_data and has_dr_data:
        step_options = [
            "1. 数据加载与分析",
            "2. 三级部位字典表生成",
            "3. 扫描方式字典表生成",
            "3.5. 部位和扫描方式映射表",
            "4. 检查项目清单生成",
            "4.5. DR检查项目清单生成",
            "5. 数据分析与质量控制"
        ]
    elif has_dr_data:
        step_options = [
            "1. 数据加载与分析",
            "4.5. DR检查项目清单生成",
            "5. 数据分析与质量控制"
        ]
    else:
        step_options = [
            "1. 数据加载与分析",
            "2. 三级部位字典表生成",
            "3. 扫描方式字典表生成",
            "3.5. 部位和扫描方式映射表",
            "4. 检查项目清单生成",
            "5. 数据分析与质量控制"
        ]

    step = st.sidebar.selectbox("选择处理步骤", step_options)

    if step == "1. 数据加载与分析":
        st.header("📊 步骤1：数据加载与分析")

        stats = processor.get_basic_stats()
        if stats:
            # CT/MR数据统计
            if has_ct_mr_data:
                st.subheader("CT/MR数据统计")
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("主数据表行数", stats.get('main_rows', 0))
                    st.metric("主数据表列数", stats.get('main_cols', 0))

                with col2:
                    st.metric("CT扫描方式数", stats.get('ct_scans', 0))
                    st.metric("MR扫描方式数", stats.get('mr_scans', 0))

                with col3:
                    st.metric("CT适用部位", stats.get('ct_parts', 0))
                    st.metric("MR适用部位", stats.get('mr_parts', 0))

                with col4:
                    st.metric("DR适用部位", stats.get('dr_applicable_parts', 0))
                    st.metric("缺失值总数", stats.get('missing_values', 0))

            # DR数据统计
            if has_dr_data:
                st.subheader("DR数据统计")
                dr_stats = processor.get_dr_stats()
                if dr_stats:
                    col1, col2, col3, col4 = st.columns(4)

                    with col1:
                        st.metric("DR数据行数", dr_stats['total_records'])
                        st.metric("有效项目数", dr_stats['valid_projects'])

                    with col2:
                        st.metric("一级部位数", dr_stats['level1_parts'])
                        st.metric("二级部位数", dr_stats['level2_parts'])

                    with col3:
                        st.metric("三级部位数", dr_stats['level3_parts'])
                        st.metric("摆位类型数", dr_stats['positions'])

                    with col4:
                        st.metric("方向编码数", dr_stats['direction_codes'])
                        st.metric("体位编码数", dr_stats['position_codes'])

            # 数据预览
            st.subheader("数据预览")

            # 根据数据类型创建标签页
            tab_names = []
            if has_ct_mr_data:
                tab_names.extend(["三级部位结构", "CT扫描方式", "MR扫描方式"])
            if has_dr_data:
                tab_names.extend(["DR数据", "方向编码", "体位编码"])

            tabs = st.tabs(tab_names)
            tab_index = 0

            if has_ct_mr_data:
                with tabs[tab_index]:
                    if processor.main_df is not None:
                        st.dataframe(processor.main_df.head(10), use_container_width=True)
                tab_index += 1

                with tabs[tab_index]:
                    if processor.ct_scan_df is not None:
                        st.dataframe(processor.ct_scan_df, use_container_width=True)
                tab_index += 1

                with tabs[tab_index]:
                    if processor.mr_scan_df is not None:
                        st.dataframe(processor.mr_scan_df, use_container_width=True)
                tab_index += 1

            if has_dr_data:
                with tabs[tab_index]:
                    if processor.dr_df is not None:
                        st.dataframe(processor.dr_df.head(10), use_container_width=True)
                tab_index += 1

                with tabs[tab_index]:
                    if processor.dr_direction_df is not None:
                        st.dataframe(processor.dr_direction_df, use_container_width=True)
                tab_index += 1

                with tabs[tab_index]:
                    if processor.dr_position_df is not None:
                        st.dataframe(processor.dr_position_df, use_container_width=True)

    elif step == "2. 三级部位字典表生成":
        st.header("🏗️ 步骤2：三级部位字典表生成")

        three_level_dict = processor.generate_three_level_dict()

        if three_level_dict is not None:
            st.success(f"成功生成三级部位字典表，共 {len(three_level_dict)} 条记录")

            # 重复编码分析
            duplicate_analysis = processor.analyze_duplicate_codes(three_level_dict)
            duplicate_stats = processor.get_duplicate_statistics(duplicate_analysis)

            # 数据质量检查
            st.subheader("数据质量检查")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                valid_codes = (three_level_dict['部位编码'].str.len() == 6).sum()
                st.metric("有效编码数", valid_codes)

            with col2:
                duplicate_codes = three_level_dict['部位编码'].duplicated().sum()
                st.metric("重复编码数", duplicate_codes)
                if duplicate_codes > 0:
                    st.warning(f"发现{duplicate_codes}个重复编码")

            with col3:
                empty_names = three_level_dict['三级部位'].isna().sum()
                st.metric("空名称数", empty_names)

            with col4:
                if duplicate_stats:
                    st.metric("重复编码种类", duplicate_stats['total_duplicates'])
                else:
                    st.metric("重复编码种类", 0)

            # 重复编码详细分析
            if duplicate_analysis is not None:
                st.subheader("🚨 重复编码详细分析")

                # 重复编码统计
                st.write("**重复编码统计信息：**")
                stats_data = {
                    "统计项目": [
                        "重复编码总数", "受影响记录数", "最大重复次数"
                    ],
                    "数值": [
                        duplicate_stats['total_duplicates'],
                        duplicate_stats['total_affected_records'],
                        duplicate_stats['max_duplicates']
                    ]
                }
                st.dataframe(pd.DataFrame(stats_data), use_container_width=True)

                # 按一级部位分组的重复编码
                st.write("**按一级部位分组的重复编码数：**")
                level1_stats = pd.DataFrame([
                    {'一级部位': k, '重复编码数': v}
                    for k, v in duplicate_stats['by_level1'].items()
                ]).sort_values('重复编码数', ascending=False)
                st.dataframe(level1_stats, use_container_width=True)

                # 重复编码详细列表
                st.write("**重复编码详细列表：**")

                # 按编码分组显示
                for code in duplicate_stats['duplicate_codes']:
                    code_data = duplicate_analysis[duplicate_analysis['部位编码'] == code]
                    with st.expander(f"编码 {code} (重复 {code_data.iloc[0]['重复次数']} 次)"):
                        st.dataframe(code_data[['序号', '一级部位', '二级部位', '三级部位', 'CT适用', 'MR适用']],
                                   use_container_width=True)

                # 下载重复编码分析
                if st.button("下载重复编码分析报告"):
                    csv = duplicate_analysis.to_csv(index=False, encoding='utf-8-sig')
                    st.download_button(
                        label="下载重复编码CSV文件",
                        data=csv,
                        file_name=f"重复编码分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                        mime="text/csv"
                    )
            else:
                st.success("✅ 未发现重复编码")

            # 搜索功能
            st.subheader("部位字典表查看")
            search_term = st.text_input("搜索部位名称")
            if search_term:
                filtered_dict = three_level_dict[
                    three_level_dict['一级部位'].str.contains(search_term, na=False) |
                    three_level_dict['二级部位'].str.contains(search_term, na=False) |
                    three_level_dict['三级部位'].str.contains(search_term, na=False)
                ]
                st.dataframe(filtered_dict, use_container_width=True)
            else:
                # 高亮显示重复编码
                if duplicate_analysis is not None:
                    duplicate_codes_list = duplicate_stats['duplicate_codes']
                    display_dict = three_level_dict.copy()

                    # 添加重复标记列
                    display_dict['重复标记'] = display_dict['部位编码'].apply(
                        lambda x: '🚨 重复' if x in duplicate_codes_list else ''
                    )

                    st.dataframe(display_dict, use_container_width=True)
                else:
                    st.dataframe(three_level_dict, use_container_width=True)

            # 下载功能
            if st.button("下载三级部位字典表"):
                csv = three_level_dict.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载CSV文件",
                    data=csv,
                    file_name=f"三级部位字典表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        else:
            st.error("无法生成三级部位字典表")

    elif step == "3. 扫描方式字典表生成":
        st.header("🔬 步骤3：扫描方式字典表生成")

        modality = st.selectbox("选择检查模态", ["CT", "MR"])
        scan_dict = processor.generate_scan_dict(modality)

        if scan_dict is not None:
            st.success(f"成功生成{modality}扫描方式字典表，共 {len(scan_dict)} 条记录")

            # 显示清理前后对比
            st.subheader("扫描方式清理效果")
            if modality == "CT":
                comparison_cols = ['CT扫描名称', '清理后名称']
            else:
                comparison_cols = ['MR成像名称', '清理后名称']

            st.dataframe(scan_dict[comparison_cols], use_container_width=True)

            # 完整字典表
            st.subheader(f"{modality}扫描方式字典表")
            st.dataframe(scan_dict, use_container_width=True)

            # 下载功能
            if st.button(f"下载{modality}扫描方式字典表"):
                csv = scan_dict.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载CSV文件",
                    data=csv,
                    file_name=f"{modality}扫描方式字典表_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        else:
            st.error(f"无法生成{modality}扫描方式字典表")

    elif step == "3.5. 部位和扫描方式映射表":
        st.header("🔗 步骤3.5：部位和扫描方式映射表")

        # 选择模态
        modality = st.selectbox("选择检查模态", ["CT", "MR"])

        # 生成映射表
        mapping = processor.create_scan_mapping(modality)

        if mapping:
            st.success(f"成功生成{modality}扫描方式映射表，共 {len(mapping)} 条映射关系")

            # 转换为DataFrame显示
            mapping_data = []
            for key, code in mapping.items():
                mapping_data.append({
                    '扫描方式名称': key,
                    '扫描编码': code
                })

            mapping_df = pd.DataFrame(mapping_data)
            st.dataframe(mapping_df, use_container_width=True)

            # 显示主数据表中的扫描方式列
            st.subheader(f"主数据表中的{modality}扫描方式列")
            if modality == 'CT':
                scan_columns = [col for col in processor.main_df.columns if col.startswith('CT-')]
            else:
                scan_columns = [col for col in processor.main_df.columns if col.startswith('MR-')]

            scan_cols_df = pd.DataFrame({
                '列名': scan_columns,
                '清理后名称': [processor.clean_scan_name(col, modality) for col in scan_columns],
                '找到编码': [processor.find_scan_code(processor.clean_scan_name(col, modality), mapping, modality) for col in scan_columns]
            })

            st.dataframe(scan_cols_df, use_container_width=True)

            # 显示匹配统计
            st.subheader("匹配统计")
            col1, col2, col3 = st.columns(3)

            with col1:
                total_cols = len(scan_columns)
                st.metric("总扫描方式数", total_cols)

            with col2:
                matched_cols = len([code for code in scan_cols_df['找到编码'] if code != '99'])
                st.metric("成功匹配数", matched_cols)

            with col3:
                unmatched_cols = len([code for code in scan_cols_df['找到编码'] if code == '99'])
                st.metric("未匹配数", unmatched_cols)
                if unmatched_cols > 0:
                    st.warning(f"有{unmatched_cols}个扫描方式未找到编码")

            # 显示未匹配的项目
            if unmatched_cols > 0:
                st.subheader("未匹配的扫描方式")
                unmatched_df = scan_cols_df[scan_cols_df['找到编码'] == '99']
                st.dataframe(unmatched_df, use_container_width=True)
        else:
            st.error(f"无法生成{modality}扫描方式映射表")

    elif step == "4. 检查项目清单生成":
        st.header("📋 步骤4：检查项目清单生成")

        with st.spinner("正在生成检查项目..."):
            ct_items_raw = processor.generate_check_items('CT')
            mr_items_raw = processor.generate_check_items('MR')

            # 格式化输出列顺序
            ct_items = processor.format_output_columns(ct_items_raw)
            mr_items = processor.format_output_columns(mr_items_raw)

        if ct_items is not None and mr_items is not None:
            # 计算无部位项目统计
            ct_no_part_count = len(ct_items[ct_items['一级部位'] == '无指定部位']) if len(ct_items) > 0 else 0
            mr_no_part_count = len(mr_items[mr_items['一级部位'] == '无指定部位']) if len(mr_items) > 0 else 0
            ct_with_part_count = len(ct_items) - ct_no_part_count
            mr_with_part_count = len(mr_items) - mr_no_part_count

            # 显示生成结果统计
            st.subheader("项目生成统计")

            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("CT项目总数", len(ct_items))
                st.caption(f"有部位: {ct_with_part_count} | 无部位: {ct_no_part_count}")
            with col2:
                st.metric("MR项目总数", len(mr_items))
                st.caption(f"有部位: {mr_with_part_count} | 无部位: {mr_no_part_count}")
            with col3:
                st.metric("总项目数量", len(ct_items) + len(mr_items))
                st.caption(f"有部位: {ct_with_part_count + mr_with_part_count}")
            with col4:
                st.metric("无部位项目", ct_no_part_count + mr_no_part_count)
                st.caption(f"CT: {ct_no_part_count} | MR: {mr_no_part_count}")

            # 无部位项目详细说明
            if ct_no_part_count > 0 or mr_no_part_count > 0:
                st.info(f"""
                ✨ **无部位项目说明**：
                - 无部位项目是基于扫描方式字典表生成的独立检查项目
                - CT无部位项目：{ct_no_part_count}个，来源于CT扫描方式字典表
                - MR无部位项目：{mr_no_part_count}个，来源于MR扫描方式字典表
                - 项目编码格式：[模态]09[二级编码][三级编码]00
                """)

            # 选择查看的项目类型
            col1, col2 = st.columns(2)
            with col1:
                item_type = st.selectbox("选择查看项目类型", ["CT检查项目", "MR检查项目"])
            with col2:
                filter_type = st.selectbox("筛选类型", ["全部项目", "有部位项目", "无部位项目"])

            if item_type == "CT检查项目":
                display_items = ct_items.copy()
                if filter_type == "有部位项目":
                    display_items = ct_items[ct_items['一级部位'] != '无指定部位']
                elif filter_type == "无部位项目":
                    display_items = ct_items[ct_items['一级部位'] == '无指定部位']

                st.subheader(f"CT检查项目清单 ({filter_type})")
                st.info("列顺序：模态 → 一级编码 → 一级部位 → 二级编码 → 二级部位 → 三级编码 → 三级部位 → 项目编码 → 项目名称")
                st.dataframe(display_items, use_container_width=True)

                # CT项目示例
                if len(display_items) > 0:
                    st.subheader(f"CT项目示例 ({filter_type})")
                    for i, (_, row) in enumerate(display_items.head(3).iterrows()):
                        st.code(f"{row['项目编码']} - {row['项目名称']}")
                        if i == 0 and filter_type == "无部位项目":
                            st.caption(f"一级部位: {row['一级部位']} | 二级部位: {row['二级部位']} | 三级部位: {row['三级部位']}")

            else:
                display_items = mr_items.copy()
                if filter_type == "有部位项目":
                    display_items = mr_items[mr_items['一级部位'] != '无指定部位']
                elif filter_type == "无部位项目":
                    display_items = mr_items[mr_items['一级部位'] == '无指定部位']

                st.subheader(f"MR检查项目清单 ({filter_type})")
                st.info("列顺序：模态 → 一级编码 → 一级部位 → 二级编码 → 二级部位 → 三级编码 → 三级部位 → 项目编码 → 项目名称")
                st.dataframe(display_items, use_container_width=True)

                # MR项目示例
                if len(display_items) > 0:
                    st.subheader(f"MR项目示例 ({filter_type})")
                    for i, (_, row) in enumerate(display_items.head(3).iterrows()):
                        st.code(f"{row['项目编码']} - {row['项目名称']}")
                        if i == 0 and filter_type == "无部位项目":
                            st.caption(f"一级部位: {row['一级部位']} | 二级部位: {row['二级部位']} | 三级部位: {row['三级部位']}")

            # 导出Excel功能
            if st.button("生成完整Excel报告"):
                try:
                    buffer = BytesIO()

                    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                        # 使用标准格式的CT和MR项目清单
                        ct_items.to_excel(writer, sheet_name='CT检查项目清单', index=False)
                        mr_items.to_excel(writer, sheet_name='MR检查项目清单', index=False)

                        # 三级部位字典
                        three_level_dict = processor.generate_three_level_dict()
                        if three_level_dict is not None:
                            three_level_dict.to_excel(writer, sheet_name='三级部位字典', index=False)

                        # 扫描方式字典
                        ct_scan_dict = processor.generate_scan_dict('CT')
                        mr_scan_dict = processor.generate_scan_dict('MR')
                        if ct_scan_dict is not None:
                            ct_scan_dict.to_excel(writer, sheet_name='CT扫描方式字典', index=False)
                        if mr_scan_dict is not None:
                            mr_scan_dict.to_excel(writer, sheet_name='MR扫描方式字典', index=False)

                        # 添加列格式说明工作表
                        format_info = pd.DataFrame({
                            '列序号': [1, 2, 3, 4, 5, 6, 7, 8, 9],
                            '列名称': ['模态', '一级编码', '一级部位', '二级编码', '二级部位', '三级编码', '三级部位', '项目编码', '项目名称'],
                            '数据类型': ['文本', '数字', '文本', '数字', '文本', '数字', '文本', '文本', '文本'],
                            '说明': [
                                'CT或MR',
                                '一级部位编码',
                                '一级部位名称',
                                '二级部位编码',
                                '二级部位名称',
                                '三级部位编码',
                                '三级部位名称',
                                '10位项目编码',
                                '标准格式项目名称'
                            ],
                            '示例': [
                                'CT',
                                '01',
                                '头部',
                                '01',
                                '颅脑',
                                '01',
                                '颅脑',
                                'CT01010110',
                                'CT颅脑(平扫)'
                            ]
                        })
                        format_info.to_excel(writer, sheet_name='列格式说明', index=False)

                    buffer.seek(0)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    st.success("✅ Excel报告生成成功！包含标准9列格式的CT和MR检查项目清单")
                    st.download_button(
                        label="下载Excel报告",
                        data=buffer.getvalue(),
                        file_name=f"检查项目清单_{timestamp}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )

                except Exception as e:
                    st.error(f"生成Excel报告失败：{str(e)}")
        else:
            st.error("检查项目生成失败")

    elif step == "4.5. DR检查项目清单生成":
        st.header("🩻 步骤4.5：DR检查项目清单生成")

        if not has_dr_data:
            st.warning("未检测到DR数据，请上传包含DR、方向、体位工作表的Excel文件")
            return

        if DRProcessor is None:
            st.error("DR处理器未正确导入，请检查dr_processor.py文件")
            return

        # 部位编码统一化选项
        st.subheader("部位编码统一化设置")

        if has_ct_mr_data:
            use_standard_codes = st.checkbox(
                "使用CT/MR标准部位编码体系",
                value=True,
                help="启用此选项将DR部位映射到CT/MR三级部位字典，确保编码体系统一"
            )

            if use_standard_codes:
                st.info("✅ 将使用CT/MR三级部位字典进行部位编码统一化处理")
            else:
                st.warning("⚠️ 将使用DR原有的部位编码，可能与CT/MR编码不一致")
        else:
            use_standard_codes = False
            st.warning("⚠️ 未检测到CT/MR数据，将使用DR原有的部位编码")

        # 生成DR检查项目清单
        with st.spinner("正在生成DR检查项目清单..."):
            dr_items, mapping_results = processor.generate_dr_items(use_standard_codes)

        if dr_items is not None and len(dr_items) > 0:
            # 格式化输出列顺序
            dr_items_formatted = processor.format_output_columns(dr_items)

            # 部位映射结果显示
            if use_standard_codes and mapping_results is not None:
                st.subheader("部位编码统一化结果")

                # 映射统计
                mapping_stats = mapping_results['映射状态'].value_counts()
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.metric("映射成功", mapping_stats.get('成功', 0))
                with col2:
                    st.metric("模糊匹配", mapping_stats.get('模糊匹配', 0))
                with col3:
                    st.metric("映射失败", mapping_stats.get('失败', 0))

                # 显示映射详情
                if st.expander("查看部位映射详情", expanded=False):
                    # 筛选选项
                    status_filter = st.selectbox(
                        "筛选映射状态",
                        ["全部", "成功", "模糊匹配", "失败"]
                    )

                    # 筛选数据
                    if status_filter != "全部":
                        filtered_mapping = mapping_results[mapping_results['映射状态'] == status_filter]
                    else:
                        filtered_mapping = mapping_results

                    # 显示映射结果表格
                    display_columns = [
                        'DR项目名称', 'DR三级部位', '映射状态',
                        '标准三级部位', '标准部位编码', 'DR适用性', '错误信息'
                    ]
                    st.dataframe(
                        filtered_mapping[display_columns].head(20),
                        use_container_width=True
                    )

                    # 显示失败的映射
                    failed_mappings = mapping_results[mapping_results['映射状态'] == '失败']
                    if len(failed_mappings) > 0:
                        st.warning(f"⚠️ 发现 {len(failed_mappings)} 个映射失败的项目，这些项目将被跳过")
                        with st.expander("查看映射失败的项目"):
                            st.dataframe(
                                failed_mappings[['DR项目名称', 'DR三级部位', '错误信息']],
                                use_container_width=True
                            )

            # 统计信息
            st.subheader("DR项目生成统计")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("DR项目总数", len(dr_items))
            with col2:
                st.metric("一级部位数", len(dr_items['一级部位'].unique()))
            with col3:
                st.metric("二级部位数", len(dr_items['二级部位'].unique()))
            with col4:
                st.metric("三级部位数", len(dr_items['三级部位'].unique()))

            # 部位分布统计
            st.subheader("部位分布统计")
            part_stats = dr_items['一级部位'].value_counts()
            col1, col2 = st.columns([1, 1])

            with col1:
                st.write("**一级部位分布**")
                for part, count in part_stats.items():
                    st.write(f"- {part}: {count}个项目")

            with col2:
                st.write("**摆位编码分布**")
                pose_stats = dr_items['摆位编码'].value_counts().head(10)
                for pose, count in pose_stats.items():
                    st.write(f"- {pose}: {count}个项目")

            # 项目预览
            st.subheader("DR检查项目清单预览")
            st.info("列顺序：模态 → 一级编码 → 一级部位 → 二级编码 → 二级部位 → 三级编码 → 三级部位 → 项目编码 → 项目名称")

            # 筛选选项
            col1, col2 = st.columns(2)
            with col1:
                selected_part = st.selectbox(
                    "选择一级部位",
                    ["全部"] + list(dr_items['一级部位'].unique())
                )
            with col2:
                show_count = st.slider("显示项目数量", 10, 100, 20)

            # 筛选数据
            if selected_part != "全部":
                filtered_items = dr_items_formatted[dr_items_formatted['一级部位'] == selected_part]
            else:
                filtered_items = dr_items_formatted

            st.dataframe(filtered_items.head(show_count), use_container_width=True)

            # 项目示例
            st.subheader("DR项目示例")
            example_items = dr_items.head(5)
            for i, (_, row) in enumerate(example_items.iterrows()):
                st.code(f"{row['项目编码']} - {row['项目名称']}")
                if i == 0:
                    st.caption(f"部位编码: {row['部位编码']} | 摆位编码: {row['摆位编码']} | 方向: {row['方向编码']} | 体位: {row['体位编码']}")

            # 生成Excel报告
            st.subheader("导出DR检查项目清单")
            if st.button("生成Excel报告", key="dr_excel"):
                try:
                    buffer = BytesIO()
                    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
                        # 主要输出：标准格式的DR检查项目清单
                        dr_items_formatted.to_excel(writer, sheet_name='DR检查项目清单', index=False)

                        # 详细信息：包含所有字段的完整数据
                        dr_items.to_excel(writer, sheet_name='DR检查项目详细信息', index=False)

                        # 部位编码字典
                        part_dict = dr_items[['一级编码', '一级部位', '二级编码', '二级部位',
                                             '三级编码', '三级部位', '部位编码']].drop_duplicates()
                        part_dict.to_excel(writer, sheet_name='DR部位编码字典', index=False)

                        # 摆位编码字典
                        position_dict = dr_items[['摆位编码', '方向编码', '体位编码',
                                                 '原始摆位', '清理后摆位']].drop_duplicates()
                        position_dict.to_excel(writer, sheet_name='DR摆位编码字典', index=False)

                        # 部位映射结果（如果有）
                        if use_standard_codes and mapping_results is not None:
                            mapping_results.to_excel(writer, sheet_name='部位映射结果', index=False)

                        # 统计信息
                        stats_data = [
                            ['DR项目总数', len(dr_items)],
                            ['一级部位数', len(dr_items['一级部位'].unique())],
                            ['二级部位数', len(dr_items['二级部位'].unique())],
                            ['三级部位数', len(dr_items['三级部位'].unique())],
                            ['摆位类型数', len(dr_items['摆位编码'].unique())],
                            ['方向编码数', len(dr_items['方向编码'].unique())],
                            ['体位编码数', len(dr_items['体位编码'].unique())]
                        ]
                        stats_df = pd.DataFrame(stats_data, columns=['统计项目', '数值'])
                        stats_df.to_excel(writer, sheet_name='统计信息', index=False)

                        # 列格式说明
                        format_info = pd.DataFrame({
                            '字段名': [
                                '模态',
                                '一级编码',
                                '一级部位',
                                '二级编码',
                                '二级部位',
                                '三级编码',
                                '三级部位',
                                '项目编码',
                                '项目名称'
                            ],
                            '示例': [
                                'DR',
                                '01',
                                '头部',
                                '01',
                                '颅脑',
                                '01',
                                '头颅',
                                'DR01010100',
                                'DR头颅(后前正位)'
                            ],
                            '说明': [
                                'DR检查模态标识',
                                '一级部位编码（2位）',
                                '一级部位名称',
                                '二级部位编码（2位）',
                                '二级部位名称',
                                '三级部位编码（2位）',
                                '三级部位名称',
                                'DR+6位部位编码+2位摆位编码',
                                'DR+三级部位+(摆位名称)'
                            ]
                        })
                        format_info.to_excel(writer, sheet_name='列格式说明', index=False)

                    buffer.seek(0)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    st.success("✅ DR检查项目清单Excel报告生成成功！")
                    st.download_button(
                        label="下载DR检查项目清单",
                        data=buffer.getvalue(),
                        file_name=f"DR检查项目清单_{timestamp}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )

                except Exception as e:
                    st.error(f"生成Excel报告失败：{str(e)}")
        else:
            st.error("DR检查项目生成失败")

    elif step == "5. 数据分析与质量控制":
        st.header("🔍 步骤5：数据分析与质量控制")

        ct_items_raw = processor.generate_check_items('CT')
        mr_items_raw = processor.generate_check_items('MR')
        three_level_dict = processor.generate_three_level_dict()

        # 格式化输出列顺序（用于显示）
        ct_items = processor.format_output_columns(ct_items_raw)
        mr_items = processor.format_output_columns(mr_items_raw)

        if ct_items is not None and mr_items is not None:
            # 综合统计报告
            st.subheader("综合统计报告")

            # 计算无部位项目统计
            ct_no_part_count = len(ct_items[ct_items['一级部位'] == '无指定部位']) if len(ct_items) > 0 else 0
            mr_no_part_count = len(mr_items[mr_items['一级部位'] == '无指定部位']) if len(mr_items) > 0 else 0
            ct_with_part_count = len(ct_items) - ct_no_part_count
            mr_with_part_count = len(mr_items) - mr_no_part_count

            stats_data = {
                "统计项目": [
                    "CT项目总数", "MR项目总数", "总项目数",
                    "CT有部位项目", "CT无部位项目", "MR有部位项目", "MR无部位项目",
                    "涉及一级部位数", "涉及CT扫描方式数", "涉及MR扫描方式数"
                ],
                "统计值": [
                    len(ct_items), len(mr_items), len(ct_items) + len(mr_items),
                    ct_with_part_count, ct_no_part_count, mr_with_part_count, mr_no_part_count,
                    len(ct_items['一级部位'].unique()) if len(ct_items) > 0 else 0,
                    len(ct_items_raw['扫描方式清理名'].unique()) if len(ct_items_raw) > 0 else 0,
                    len(mr_items_raw['扫描方式清理名'].unique()) if len(mr_items_raw) > 0 else 0
                ]
            }

            stats_df = pd.DataFrame(stats_data)
            st.dataframe(stats_df, use_container_width=True)

            # 部位编码重复问题分析
            st.subheader("🚨 部位编码重复问题分析")

            if three_level_dict is not None:
                duplicate_analysis = processor.analyze_duplicate_codes(three_level_dict)
                duplicate_stats = processor.get_duplicate_statistics(duplicate_analysis)

                if duplicate_analysis is not None:
                    st.error(f"发现 {duplicate_stats['total_duplicates']} 个重复的部位编码，影响 {duplicate_stats['total_affected_records']} 条记录")

                    # 重复编码对项目生成的影响
                    st.write("**重复编码对项目生成的潜在影响：**")
                    impact_analysis = []

                    for code in duplicate_stats['duplicate_codes']:
                        code_data = duplicate_analysis[duplicate_analysis['部位编码'] == code]
                        ct_applicable = (code_data['CT适用'] == '是').sum()
                        mr_applicable = (code_data['MR适用'] == '是').sum()

                        impact_analysis.append({
                            '重复编码': code,
                            '重复次数': code_data.iloc[0]['重复次数'],
                            '涉及部位': ', '.join(code_data['三级部位'].tolist()),
                            'CT适用数': ct_applicable,
                            'MR适用数': mr_applicable,
                            '潜在项目冲突': '是' if (ct_applicable > 1 or mr_applicable > 1) else '否'
                        })

                    impact_df = pd.DataFrame(impact_analysis)
                    st.dataframe(impact_df, use_container_width=True)

                    # 建议解决方案
                    st.write("**建议解决方案：**")
                    st.info("""
                    1. **数据清理**：检查重复编码是否为数据录入错误
                    2. **编码重新分配**：为重复的部位分配新的唯一编码
                    3. **部位合并**：如果重复编码对应相同部位，考虑合并记录
                    4. **优先级设定**：如果必须保留重复，设定优先级规则
                    """)
                else:
                    st.success("✅ 未发现部位编码重复问题")

            # 错误分析
            st.subheader("项目编码错误分析")

            errors = []

            # 检查编码格式错误
            if len(ct_items) > 0:
                invalid_ct_codes = ct_items[ct_items['项目编码'].str.len() != 10]
                if len(invalid_ct_codes) > 0:
                    errors.append(f"CT编码格式错误: {len(invalid_ct_codes)}项")

            if len(mr_items) > 0:
                invalid_mr_codes = mr_items[mr_items['项目编码'].str.len() != 10]
                if len(invalid_mr_codes) > 0:
                    errors.append(f"MR编码格式错误: {len(invalid_mr_codes)}项")

            # 检查项目编码重复
            all_codes = []
            if len(ct_items) > 0:
                all_codes.extend(ct_items['项目编码'].tolist())
            if len(mr_items) > 0:
                all_codes.extend(mr_items['项目编码'].tolist())

            duplicate_project_codes = len(all_codes) - len(set(all_codes))
            if duplicate_project_codes > 0:
                errors.append(f"项目编码重复: {duplicate_project_codes}项")

                # 显示重复的项目编码
                code_counts = pd.Series(all_codes).value_counts()
                duplicate_project_codes_list = code_counts[code_counts > 1]

                if len(duplicate_project_codes_list) > 0:
                    st.write("**重复的项目编码：**")
                    duplicate_project_df = pd.DataFrame({
                        '项目编码': duplicate_project_codes_list.index,
                        '重复次数': duplicate_project_codes_list.values
                    })
                    st.dataframe(duplicate_project_df, use_container_width=True)

            # 检查扫描编码匹配问题
            ct_mapping = processor.create_scan_mapping('CT')
            mr_mapping = processor.create_scan_mapping('MR')

            ct_unmatched = 0
            mr_unmatched = 0

            if len(ct_items_raw) > 0:
                ct_unmatched = (ct_items_raw['扫描编码'] == '99').sum()
            if len(mr_items_raw) > 0:
                mr_unmatched = (mr_items_raw['扫描编码'] == '99').sum()

            if ct_unmatched > 0:
                errors.append(f"CT扫描方式未匹配: {ct_unmatched}项")
            if mr_unmatched > 0:
                errors.append(f"MR扫描方式未匹配: {mr_unmatched}项")

            if errors:
                for error in errors:
                    st.error(error)
            else:
                st.success("✅ 未发现项目编码质量问题")

            # 数据完整性验证
            st.subheader("数据完整性验证")

            completeness_checks = []

            if len(ct_items) > 0:
                ct_missing = ct_items.isnull().sum().sum()
                completeness_checks.append(("CT项目数据", "完整" if ct_missing == 0 else f"缺失{ct_missing}个值"))

            if len(mr_items) > 0:
                mr_missing = mr_items.isnull().sum().sum()
                completeness_checks.append(("MR项目数据", "完整" if mr_missing == 0 else f"缺失{mr_missing}个值"))

            # 添加部位编码完整性检查
            if three_level_dict is not None:
                part_code_missing = three_level_dict['部位编码'].isna().sum()
                completeness_checks.append(("部位编码数据", "完整" if part_code_missing == 0 else f"缺失{part_code_missing}个值"))

            completeness_df = pd.DataFrame(completeness_checks, columns=["检查项", "状态"])
            st.dataframe(completeness_df, use_container_width=True)

            # 扫描方式匹配率统计
            st.subheader("扫描方式匹配率统计")

            match_stats = {
                "模态": ["CT", "MR"],
                "总扫描方式数": [
                    len(ct_items_raw['扫描方式清理名'].unique()) if len(ct_items_raw) > 0 else 0,
                    len(mr_items_raw['扫描方式清理名'].unique()) if len(mr_items_raw) > 0 else 0
                ],
                "成功匹配数": [
                    len(ct_items_raw[ct_items_raw['扫描编码'] != '99']['扫描方式清理名'].unique()) if len(ct_items_raw) > 0 else 0,
                    len(mr_items_raw[mr_items_raw['扫描编码'] != '99']['扫描方式清理名'].unique()) if len(mr_items_raw) > 0 else 0
                ],
                "匹配率": []
            }

            for i in range(2):
                total = match_stats["总扫描方式数"][i]
                matched = match_stats["成功匹配数"][i]
                rate = f"{matched/total*100:.1f}%" if total > 0 else "0%"
                match_stats["匹配率"].append(rate)

            match_df = pd.DataFrame(match_stats)
            st.dataframe(match_df, use_container_width=True)

        else:
            st.error("无法进行质量控制分析")

if __name__ == "__main__":
    main()
