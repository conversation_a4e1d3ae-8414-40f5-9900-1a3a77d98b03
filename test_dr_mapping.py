#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DR部位映射功能
验证DR部位编码统一化处理
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from streamlit_simple import SimpleMedicalProcessor

def test_dr_part_mapping():
    """测试DR部位映射功能"""
    print("="*60)
    print("测试DR部位编码统一化功能")
    print("="*60)
    
    # 创建处理器实例
    processor = SimpleMedicalProcessor()
    
    # 加载CT/MR数据
    ct_mr_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    if not os.path.exists(ct_mr_file):
        print(f"❌ CT/MR数据文件不存在: {ct_mr_file}")
        return
    
    with open(ct_mr_file, 'rb') as f:
        success, message = processor.load_data(f)
    
    if not success:
        print(f"❌ CT/MR数据加载失败: {message}")
        return
    
    print(f"✅ CT/MR数据加载成功")
    
    # 加载DR数据
    dr_file = "data/DR项目结构-0705.xlsx"
    if not os.path.exists(dr_file):
        print(f"❌ DR数据文件不存在: {dr_file}")
        return
    
    # 手动加载DR数据
    try:
        processor.dr_df = pd.read_excel(dr_file, sheet_name='DR')
        processor.dr_direction_df = pd.read_excel(dr_file, sheet_name='方向')
        processor.dr_position_df = pd.read_excel(dr_file, sheet_name='体位')
        print(f"✅ DR数据加载成功")
    except Exception as e:
        print(f"❌ DR数据加载失败: {e}")
        return
    
    # 测试三级部位字典表生成（包含DR列）
    print(f"\n📋 生成三级部位字典表...")
    three_level_dict = processor.generate_three_level_dict()
    if three_level_dict is not None:
        print(f"✅ 三级部位字典表生成成功: {len(three_level_dict)}个部位")
        
        # 检查DR适用部位
        dr_applicable = three_level_dict[three_level_dict['DR适用'] == '是']
        print(f"✅ DR适用部位数量: {len(dr_applicable)}")
        
        print("\n前10个DR适用部位:")
        for i, (_, row) in enumerate(dr_applicable.head(10).iterrows()):
            print(f"  {i+1:2d}. {row['一级部位']}-{row['二级部位']}-{row['三级部位']} (编码: {row['部位编码']})")
    else:
        print("❌ 三级部位字典表生成失败")
        return
    
    # 测试DR项目生成（使用标准编码）
    print(f"\n🔧 生成DR检查项目清单（使用标准编码）...")
    dr_items, mapping_results = processor.generate_dr_items(use_standard_codes=True)
    
    if dr_items is not None and len(dr_items) > 0:
        print(f"✅ DR项目生成成功: {len(dr_items)}个项目")
        
        # 映射结果统计
        if mapping_results is not None:
            print(f"\n📊 部位映射结果统计:")
            mapping_stats = mapping_results['映射状态'].value_counts()
            for status, count in mapping_stats.items():
                print(f"  - {status}: {count}个")
            
            # 显示映射成功的示例
            successful_mappings = mapping_results[mapping_results['映射状态'] == '成功']
            if len(successful_mappings) > 0:
                print(f"\n✅ 映射成功示例（前5个）:")
                for i, (_, row) in enumerate(successful_mappings.head(5).iterrows()):
                    print(f"  {i+1}. {row['DR项目名称']}")
                    print(f"     DR部位: {row['DR三级部位']}")
                    print(f"     标准部位: {row['标准三级部位']} (编码: {row['标准部位编码']})")
                    print(f"     DR适用性: {row['DR适用性']}")
            
            # 显示映射失败的项目
            failed_mappings = mapping_results[mapping_results['映射状态'] == '失败']
            if len(failed_mappings) > 0:
                print(f"\n⚠️ 映射失败项目（前5个）:")
                for i, (_, row) in enumerate(failed_mappings.head(5).iterrows()):
                    print(f"  {i+1}. {row['DR项目名称']}")
                    print(f"     DR部位: {row['DR三级部位']}")
                    print(f"     错误信息: {row['错误信息']}")
        
        # 显示生成的DR项目示例
        print(f"\n📝 生成的DR项目示例（前5个）:")
        for i, (_, row) in enumerate(dr_items.head(5).iterrows()):
            print(f"  {i+1}. {row['项目编码']} - {row['项目名称']}")
            print(f"     部位: {row['一级部位']}-{row['二级部位']}-{row['三级部位']}")
            print(f"     部位编码: {row['部位编码']} | 摆位编码: {row['摆位编码']}")
            if 'DR适用性' in row:
                print(f"     DR适用性: {row['DR适用性']}")
    else:
        print("❌ DR项目生成失败")
        return
    
    # 测试不使用标准编码的情况
    print(f"\n🔧 生成DR检查项目清单（不使用标准编码）...")
    dr_items_legacy, _ = processor.generate_dr_items(use_standard_codes=False)
    
    if dr_items_legacy is not None and len(dr_items_legacy) > 0:
        print(f"✅ DR项目生成成功（原有逻辑）: {len(dr_items_legacy)}个项目")
        
        print(f"\n📝 原有逻辑生成的DR项目示例（前3个）:")
        for i, (_, row) in enumerate(dr_items_legacy.head(3).iterrows()):
            print(f"  {i+1}. {row['项目编码']} - {row['项目名称']}")
            print(f"     部位编码: {row['部位编码']} | 摆位编码: {row['摆位编码']}")
    
    print(f"\n🎉 DR部位编码统一化测试完成！")

def main():
    """主函数"""
    print("🔧 DR部位编码统一化功能测试")
    print("测试目标:")
    print("1. 验证三级部位字典表包含DR适用性信息")
    print("2. 验证DR部位映射到标准编码体系")
    print("3. 验证部位编码统一化处理")
    
    try:
        test_dr_part_mapping()
        
        print("\n" + "="*60)
        print("✅ 所有测试完成")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
