import pandas as pd
import json
from collections import defaultdict

def analyze_missing_encodings():
    # 读取生成的编码结果
    with open('摆位编码表_矩阵解析生成.json', 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 统计缺失的体位和方向
    missing_positions = set()
    missing_directions = set()
    unk_projects = []
    
    # 读取现有的编码表
    excel_file = 'data/DR项目结构-0705.xlsx'
    direction_df = pd.read_excel(excel_file, sheet_name='方向')
    position_df = pd.read_excel(excel_file, sheet_name='体位')
    
    existing_positions = set(position_df['体位'].tolist())
    existing_directions = set(direction_df['方向'].tolist())
    
    print("=== 现有编码表分析 ===")
    print(f"现有体位编码数量: {len(existing_positions)}")
    print(f"现有方向编码数量: {len(existing_directions)}")
    
    # 分析每个项目的编码情况
    for result in results:
        project_name = result.get('项目名称', '')
        position_name = result.get('摆位', '')
        body_code = result.get('体位编码', '')
        dir_code = result.get('方向编码', '')
        pos_code = result.get('摆位编码', '')
        active_positions = result.get('激活的体位', [])
        active_directions = result.get('激活的方向', [])
        
        # 检查体位编码缺失
        if body_code == 'UNK' and active_positions:
            for pos in active_positions:
                if pos not in existing_positions:
                    missing_positions.add(pos)
        
        # 检查方向编码缺失
        if dir_code == 'UNK' and active_directions:
            for dir_name in active_directions:
                if dir_name not in existing_directions:
                    missing_directions.add(dir_name)
        
        # 收集包含UNK的项目
        if 'UNK' in str(pos_code) or body_code == 'UNK' or dir_code == 'UNK':
            unk_projects.append({
                '项目名称': project_name,
                '摆位': position_name,
                '摆位编码': pos_code,
                '体位编码': body_code,
                '方向编码': dir_code,
                '激活的体位': active_positions,
                '激活的方向': active_directions
            })
    
    print("\n=== 缺失编码分析 ===")
    print(f"缺失体位编码数量: {len(missing_positions)}")
    if missing_positions:
        print("缺失的体位:")
        for i, pos in enumerate(sorted(missing_positions), 1):
            print(f"  {i}. {pos}")
    
    print(f"\n缺失方向编码数量: {len(missing_directions)}")
    if missing_directions:
        print("缺失的方向:")
        for i, dir_name in enumerate(sorted(missing_directions), 1):
            print(f"  {i}. {dir_name}")
    
    print(f"\n=== 包含UNK编码的项目统计 ===")
    print(f"总计 {len(unk_projects)} 个项目包含UNK编码")
    
    # 按问题类型分类
    body_unk_only = []
    dir_unk_only = []
    both_unk = []
    
    for proj in unk_projects:
        body_is_unk = proj['体位编码'] == 'UNK'
        dir_is_unk = proj['方向编码'] == 'UNK'
        
        if body_is_unk and dir_is_unk:
            both_unk.append(proj)
        elif body_is_unk:
            body_unk_only.append(proj)
        elif dir_is_unk:
            dir_unk_only.append(proj)
    
    print(f"\n仅体位编码为UNK: {len(body_unk_only)} 个")
    print(f"仅方向编码为UNK: {len(dir_unk_only)} 个")
    print(f"体位和方向都为UNK: {len(both_unk)} 个")
    
    # 详细列出问题项目
    if body_unk_only:
        print("\n=== 仅体位编码缺失的项目 ===")
        for i, proj in enumerate(body_unk_only, 1):
            print(f"{i}. {proj['项目名称']} | 摆位: {proj['摆位']} | 激活体位: {proj['激活的体位']}")
    
    if dir_unk_only:
        print("\n=== 仅方向编码缺失的项目 ===")
        for i, proj in enumerate(dir_unk_only, 1):
            print(f"{i}. {proj['项目名称']} | 摆位: {proj['摆位']} | 激活方向: {proj['激活的方向']}")
    
    if both_unk:
        print("\n=== 体位和方向都缺失的项目 ===")
        for i, proj in enumerate(both_unk, 1):
            print(f"{i}. {proj['项目名称']} | 摆位: {proj['摆位']} | 激活体位: {proj['激活的体位']} | 激活方向: {proj['激活的方向']}")
    
    # 生成建议的编码补充
    print("\n=== 建议补充的编码 ===")
    if missing_positions:
        print("\n建议在体位编码表中添加:")
        for i, pos in enumerate(sorted(missing_positions), 1):
            # 建议编码（这里使用简单的数字编码，实际应根据业务规则确定）
            suggested_code = f"X{i}"
            print(f"  {pos} -> {suggested_code}")
    
    if missing_directions:
        print("\n建议在方向编码表中添加:")
        for i, dir_name in enumerate(sorted(missing_directions), 1):
            # 建议编码（这里使用简单的数字编码，实际应根据业务规则确定）
            suggested_code = f"Y{i}"
            print(f"  {dir_name} -> {suggested_code}")

if __name__ == "__main__":
    print("=== 缺失编码分析报告 ===")
    analyze_missing_encodings()