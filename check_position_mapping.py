import pandas as pd

# 读取体位编码表
df_pos = pd.read_excel('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx', sheet_name='体位')
print('体位编码映射:')
for idx, row in df_pos.iterrows():
    print(f'{row["体位"]} -> {row["体位编码"]}')

print('\n检查仰卧位是否存在:')
print('仰卧位' in df_pos['体位'].values)

# 创建映射字典
position_code_mapping = dict(zip(df_pos['体位'], df_pos['体位编码']))
print(f'\n仰卧位的编码: {position_code_mapping.get("仰卧位", "未找到")}')

# 检查所有包含'卧'的体位
print('\n包含"卧"的体位:')
for pos in df_pos['体位']:
    if '卧' in str(pos):
        print(f'{pos} -> {position_code_mapping[pos]}')