#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DR智能映射分析工具
基于DR数据文件中的三级部位信息，与NEW检查项目名称结构表中的标准三级部位字典进行智能映射分析
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys
from io import BytesIO

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from dr_manager import DRManager
from dr_mapping_validator import DRMappingValidator
from streamlit_simple import SimpleMedicalProcessor


class DRIntelligentMappingAnalyzer:
    """DR智能映射分析器"""
    
    def __init__(self):
        self.dr_manager = DRManager()
        self.processor = SimpleMedicalProcessor()
        self.analysis_results = {}
        
    def load_data_files(self, dr_file_path: str, standard_file_path: str):
        """加载数据文件"""
        print("📁 正在加载数据文件...")
        
        # 加载DR数据
        success, message = self.dr_manager.load_dr_data(dr_file_path)
        if not success:
            raise Exception(f"DR数据加载失败：{message}")
        print(f"✅ {message}")
        
        # 加载标准字典数据
        with open(standard_file_path, 'rb') as f:
            success, message = self.processor.load_data(f)
        if not success:
            raise Exception(f"标准字典数据加载失败：{message}")
        print(f"✅ 标准字典数据加载成功")
        
        # 生成三级部位字典
        three_level_dict = self.processor.generate_three_level_dict()
        if three_level_dict is None:
            raise Exception("三级部位字典生成失败")
        
        # 设置标准字典到DR管理器
        success, message = self.dr_manager.set_standard_dict(three_level_dict)
        if not success:
            raise Exception(f"标准字典设置失败：{message}")
        print(f"✅ {message}")
        
        return True
    
    def execute_intelligent_mapping(self):
        """执行智能映射分析"""
        print("\n🔍 正在执行智能映射分析...")
        
        # 执行双向映射验证
        success, message = self.dr_manager.validate_part_mapping()
        if not success:
            raise Exception(f"映射验证失败：{message}")
        
        print(f"✅ {message}")
        
        # 获取映射统计
        mapping_stats = self.dr_manager.get_mapping_statistics()
        self.analysis_results['mapping_stats'] = mapping_stats
        
        # 获取差异详情
        diff_details = self.dr_manager.get_difference_details()
        self.analysis_results['diff_details'] = diff_details
        
        return True
    
    def generate_standardized_dr_items(self):
        """生成标准化DR项目清单"""
        print("\n🔧 正在生成标准化DR项目清单...")
        
        # 使用标准编码生成DR项目
        dr_items, message = self.dr_manager.generate_dr_items(use_standard_codes=True)
        if dr_items is None:
            raise Exception(f"DR项目生成失败：{message}")
        
        print(f"✅ {message}")
        self.analysis_results['dr_items'] = dr_items
        
        return dr_items
    
    def analyze_position_mapping(self):
        """分析摆位编码映射"""
        print("\n📊 正在分析摆位编码映射...")
        
        dr_items = self.analysis_results.get('dr_items')
        if dr_items is None:
            return None
        
        # 摆位编码分析
        position_analysis = {
            'total_positions': len(dr_items['摆位编码'].unique()) if '摆位编码' in dr_items.columns else 0,
            'direction_codes': len(dr_items['方向编码'].unique()) if '方向编码' in dr_items.columns else 0,
            'position_codes': len(dr_items['体位编码'].unique()) if '体位编码' in dr_items.columns else 0,
        }
        
        # 摆位编码详情
        if '摆位编码' in dr_items.columns:
            position_details = dr_items[[
                '摆位编码', '方向编码', '体位编码', 
                '原始摆位', '清理后摆位'
            ]].drop_duplicates().sort_values('摆位编码')
            
            position_analysis['position_details'] = position_details
            
            # 摆位分布统计
            position_distribution = dr_items['摆位编码'].value_counts()
            position_analysis['position_distribution'] = position_distribution
        
        self.analysis_results['position_analysis'] = position_analysis
        print(f"✅ 摆位编码分析完成：{position_analysis['total_positions']}个摆位编码")
        
        return position_analysis
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n📋 正在生成综合分析报告...")
        
        mapping_stats = self.analysis_results.get('mapping_stats', {})
        diff_details = self.analysis_results.get('diff_details', {})
        dr_items = self.analysis_results.get('dr_items')
        position_analysis = self.analysis_results.get('position_analysis', {})
        
        # 创建报告数据
        report = {
            'analysis_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'data_summary': {},
            'mapping_summary': {},
            'position_summary': {},
            'quality_assessment': {}
        }
        
        # 数据摘要
        if dr_items is not None:
            report['data_summary'] = {
                'total_dr_projects': len(dr_items),
                'unique_level1_parts': len(dr_items['一级部位'].unique()),
                'unique_level2_parts': len(dr_items['二级部位'].unique()),
                'unique_level3_parts': len(dr_items['三级部位'].unique()),
                'unique_position_codes': len(dr_items['摆位编码'].unique()) if '摆位编码' in dr_items.columns else 0
            }
        
        # 映射摘要
        dr_to_std = mapping_stats.get('dr_to_standard', {})
        std_to_dr = mapping_stats.get('standard_to_dr', {})
        
        report['mapping_summary'] = {
            'dr_to_standard': {
                'total_items': dr_to_std.get('总DR部位数', 0),
                'exact_matches': dr_to_std.get('映射成功', 0),
                'fuzzy_matches': dr_to_std.get('模糊匹配', 0),
                'failed_matches': dr_to_std.get('映射失败', 0),
                'success_rate': dr_to_std.get('成功率', 0)
            },
            'standard_to_dr': {
                'total_applicable': std_to_dr.get('标准DR适用部位数', 0),
                'covered_in_dr': std_to_dr.get('DR数据中存在', 0),
                'missing_in_dr': std_to_dr.get('DR数据中缺失', 0),
                'coverage_rate': std_to_dr.get('覆盖率', 0)
            }
        }
        
        # 摆位摘要
        report['position_summary'] = {
            'total_positions': position_analysis.get('total_positions', 0),
            'direction_codes': position_analysis.get('direction_codes', 0),
            'position_codes': position_analysis.get('position_codes', 0)
        }
        
        # 质量评估
        success_rate = dr_to_std.get('成功率', 0)
        coverage_rate = std_to_dr.get('覆盖率', 0)
        
        if success_rate >= 95 and coverage_rate >= 85:
            quality_level = "优秀"
        elif success_rate >= 90 and coverage_rate >= 80:
            quality_level = "良好"
        elif success_rate >= 85 and coverage_rate >= 75:
            quality_level = "一般"
        else:
            quality_level = "需要改进"
        
        report['quality_assessment'] = {
            'overall_quality': quality_level,
            'mapping_quality': "优秀" if success_rate >= 95 else "良好" if success_rate >= 90 else "一般",
            'coverage_quality': "优秀" if coverage_rate >= 85 else "良好" if coverage_rate >= 80 else "一般",
            'recommendations': self._generate_recommendations(diff_details)
        }
        
        self.analysis_results['comprehensive_report'] = report
        print(f"✅ 综合分析报告生成完成")
        
        return report
    
    def _generate_recommendations(self, diff_details):
        """生成改进建议"""
        recommendations = []
        
        if not diff_details.get('missing_in_standard', pd.DataFrame()).empty:
            count = len(diff_details['missing_in_standard'])
            recommendations.append(f"建议在标准字典中添加{count}个缺失的DR部位")
        
        if not diff_details.get('missing_in_dr', pd.DataFrame()).empty:
            count = len(diff_details['missing_in_dr'])
            recommendations.append(f"建议在DR数据中补充{count}个标准部位的检查项目")
        
        if not diff_details.get('fuzzy_matches', pd.DataFrame()).empty:
            count = len(diff_details['fuzzy_matches'])
            recommendations.append(f"建议人工确认{count}个模糊匹配的部位映射关系")
        
        if not diff_details.get('dr_not_applicable', pd.DataFrame()).empty:
            count = len(diff_details['dr_not_applicable'])
            recommendations.append(f"建议检查{count}个标记为不适用DR的部位数据")
        
        if not recommendations:
            recommendations.append("数据质量良好，无需特别改进")
        
        return recommendations
    
    def export_excel_report(self, output_path: str = None):
        """导出Excel综合报告"""
        print("\n📄 正在导出Excel综合报告...")
        
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"output/DR智能映射分析报告_{timestamp}.xlsx"
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        try:
            # 使用DR管理器的综合报告导出功能
            buffer = self.dr_manager.export_comprehensive_report()
            
            # 添加分析报告工作表
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                # 先写入DR管理器的报告
                if buffer:
                    # 读取buffer中的数据并写入
                    temp_file = BytesIO(buffer.getvalue())
                    with pd.ExcelFile(temp_file) as temp_excel:
                        for sheet_name in temp_excel.sheet_names:
                            df = pd.read_excel(temp_excel, sheet_name=sheet_name)
                            df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 添加智能映射分析报告
                self._add_analysis_sheets(writer)
            
            print(f"✅ Excel报告已导出到：{output_path}")
            return output_path
            
        except Exception as e:
            print(f"❌ Excel报告导出失败：{str(e)}")
            return None
    
    def _add_analysis_sheets(self, writer):
        """添加分析报告工作表"""
        # 综合分析报告
        report = self.analysis_results.get('comprehensive_report', {})
        if report:
            # 创建报告摘要
            summary_data = []
            
            # 数据摘要
            data_summary = report.get('data_summary', {})
            for key, value in data_summary.items():
                summary_data.append(['数据摘要', key, value])
            
            # 映射摘要
            mapping_summary = report.get('mapping_summary', {})
            for category, stats in mapping_summary.items():
                for key, value in stats.items():
                    summary_data.append(['映射摘要', f"{category}_{key}", value])
            
            # 摆位摘要
            position_summary = report.get('position_summary', {})
            for key, value in position_summary.items():
                summary_data.append(['摆位摘要', key, value])
            
            # 质量评估
            quality_assessment = report.get('quality_assessment', {})
            for key, value in quality_assessment.items():
                if key != 'recommendations':
                    summary_data.append(['质量评估', key, value])
            
            summary_df = pd.DataFrame(summary_data, columns=['类别', '指标', '数值'])
            summary_df.to_excel(writer, sheet_name='智能映射分析摘要', index=False)
            
            # 改进建议
            recommendations = quality_assessment.get('recommendations', [])
            if recommendations:
                rec_df = pd.DataFrame({
                    '序号': range(1, len(recommendations) + 1),
                    '改进建议': recommendations
                })
                rec_df.to_excel(writer, sheet_name='改进建议', index=False)
        
        # 摆位编码详情
        position_analysis = self.analysis_results.get('position_analysis', {})
        position_details = position_analysis.get('position_details')
        if position_details is not None:
            position_details.to_excel(writer, sheet_name='摆位编码详细映射', index=False)
        
        # 摆位分布统计
        position_distribution = position_analysis.get('position_distribution')
        if position_distribution is not None:
            dist_df = pd.DataFrame({
                '摆位编码': position_distribution.index,
                '使用次数': position_distribution.values
            })
            dist_df.to_excel(writer, sheet_name='摆位编码使用统计', index=False)
    
    def print_analysis_summary(self):
        """打印分析摘要"""
        print("\n" + "="*80)
        print("DR智能映射分析结果摘要")
        print("="*80)
        
        report = self.analysis_results.get('comprehensive_report', {})
        
        # 数据摘要
        data_summary = report.get('data_summary', {})
        print(f"\n📊 数据摘要:")
        print(f"  - DR项目总数: {data_summary.get('total_dr_projects', 0)}")
        print(f"  - 一级部位数: {data_summary.get('unique_level1_parts', 0)}")
        print(f"  - 二级部位数: {data_summary.get('unique_level2_parts', 0)}")
        print(f"  - 三级部位数: {data_summary.get('unique_level3_parts', 0)}")
        print(f"  - 摆位编码数: {data_summary.get('unique_position_codes', 0)}")
        
        # 映射结果
        mapping_summary = report.get('mapping_summary', {})
        dr_to_std = mapping_summary.get('dr_to_standard', {})
        std_to_dr = mapping_summary.get('standard_to_dr', {})
        
        print(f"\n🔍 映射分析结果:")
        print(f"  DR → 标准字典映射:")
        print(f"    - 总项目数: {dr_to_std.get('total_items', 0)}")
        print(f"    - 精确匹配: {dr_to_std.get('exact_matches', 0)}")
        print(f"    - 模糊匹配: {dr_to_std.get('fuzzy_matches', 0)}")
        print(f"    - 映射失败: {dr_to_std.get('failed_matches', 0)}")
        print(f"    - 成功率: {dr_to_std.get('success_rate', 0):.1f}%")
        
        print(f"  标准字典 → DR数据覆盖:")
        print(f"    - 标准DR适用部位: {std_to_dr.get('total_applicable', 0)}")
        print(f"    - DR数据中存在: {std_to_dr.get('covered_in_dr', 0)}")
        print(f"    - DR数据中缺失: {std_to_dr.get('missing_in_dr', 0)}")
        print(f"    - 覆盖率: {std_to_dr.get('coverage_rate', 0):.1f}%")
        
        # 摆位分析
        position_summary = report.get('position_summary', {})
        print(f"\n📋 摆位编码分析:")
        print(f"  - 摆位编码总数: {position_summary.get('total_positions', 0)}")
        print(f"  - 方向编码数: {position_summary.get('direction_codes', 0)}")
        print(f"  - 体位编码数: {position_summary.get('position_codes', 0)}")
        
        # 质量评估
        quality_assessment = report.get('quality_assessment', {})
        print(f"\n⭐ 质量评估:")
        print(f"  - 整体质量: {quality_assessment.get('overall_quality', 'N/A')}")
        print(f"  - 映射质量: {quality_assessment.get('mapping_quality', 'N/A')}")
        print(f"  - 覆盖质量: {quality_assessment.get('coverage_quality', 'N/A')}")
        
        # 改进建议
        recommendations = quality_assessment.get('recommendations', [])
        if recommendations:
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        print("\n" + "="*80)


def main():
    """主函数"""
    print("🔧 DR智能映射分析工具")
    print("基于DR数据文件中的三级部位信息，与NEW检查项目名称结构表中的标准三级部位字典进行智能映射分析")
    print("="*80)
    
    # 数据文件路径
    dr_file = "data/DR项目结构-0705.xlsx"
    standard_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(dr_file):
        print(f"❌ DR数据文件不存在: {dr_file}")
        return
    
    if not os.path.exists(standard_file):
        print(f"❌ 标准字典文件不存在: {standard_file}")
        return
    
    try:
        # 创建分析器
        analyzer = DRIntelligentMappingAnalyzer()
        
        # 1. 加载数据文件
        analyzer.load_data_files(dr_file, standard_file)
        
        # 2. 执行智能映射分析
        analyzer.execute_intelligent_mapping()
        
        # 3. 生成标准化DR项目清单
        analyzer.generate_standardized_dr_items()
        
        # 4. 分析摆位编码映射
        analyzer.analyze_position_mapping()
        
        # 5. 生成综合分析报告
        analyzer.generate_comprehensive_report()
        
        # 6. 打印分析摘要
        analyzer.print_analysis_summary()
        
        # 7. 导出Excel报告
        output_file = analyzer.export_excel_report()
        
        if output_file:
            print(f"\n🎉 DR智能映射分析完成！")
            print(f"📄 详细报告已保存到: {output_file}")
        else:
            print(f"\n⚠️ 分析完成，但Excel报告导出失败")
        
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
