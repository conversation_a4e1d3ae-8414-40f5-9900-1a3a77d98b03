#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的DR处理架构
验证独立模块的功能和集成
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from dr_manager import DRManager
from dr_mapping_validator import DRMappingValidator
from streamlit_simple import SimpleMedicalProcessor

def test_dr_modules():
    """测试DR模块功能"""
    print("="*60)
    print("测试重构后的DR处理架构")
    print("="*60)
    
    # 1. 测试DR管理器
    print("\n📋 测试DR管理器...")
    dr_manager = DRManager()
    
    # 加载DR数据
    dr_file = "data/DR项目结构-0705.xlsx"
    if not os.path.exists(dr_file):
        print(f"❌ DR数据文件不存在: {dr_file}")
        return
    
    success, message = dr_manager.load_dr_data(dr_file)
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
        return
    
    # 获取基础统计
    stats = dr_manager.get_basic_stats()
    print(f"📊 DR数据统计:")
    for key, value in stats.items():
        print(f"  - {key}: {value}")
    
    # 2. 测试标准字典设置
    print(f"\n📋 测试标准字典设置...")
    
    # 加载CT/MR数据
    processor = SimpleMedicalProcessor()
    ct_mr_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    if not os.path.exists(ct_mr_file):
        print(f"❌ CT/MR数据文件不存在: {ct_mr_file}")
        return
    
    with open(ct_mr_file, 'rb') as f:
        success, message = processor.load_data(f)
    
    if not success:
        print(f"❌ CT/MR数据加载失败: {message}")
        return
    
    # 生成三级部位字典
    three_level_dict = processor.generate_three_level_dict()
    if three_level_dict is not None:
        print(f"✅ 三级部位字典生成成功: {len(three_level_dict)}个部位")
        
        # 设置到DR管理器
        success, message = dr_manager.set_standard_dict(three_level_dict)
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
            return
    else:
        print("❌ 三级部位字典生成失败")
        return
    
    # 3. 测试部位映射验证
    print(f"\n🔍 测试部位映射验证...")
    success, message = dr_manager.validate_part_mapping()
    if success:
        print(f"✅ {message}")
        
        # 获取映射统计
        mapping_stats = dr_manager.get_mapping_statistics()
        print(f"📊 映射统计:")
        for category, stats in mapping_stats.items():
            print(f"  {category}:")
            for key, value in stats.items():
                print(f"    - {key}: {value}")
        
        # 获取差异详情
        diff_details = dr_manager.get_difference_details()
        print(f"📋 差异详情:")
        print(f"  - DR部位缺失: {len(diff_details['missing_in_standard'])}个")
        print(f"  - 标准部位缺失: {len(diff_details['missing_in_dr'])}个")
        print(f"  - 模糊匹配: {len(diff_details['fuzzy_matches'])}个")
        print(f"  - DR不适用: {len(diff_details['dr_not_applicable'])}个")
    else:
        print(f"❌ {message}")
        return
    
    # 4. 测试DR项目生成（使用标准编码）
    print(f"\n🔧 测试DR项目生成（标准编码）...")
    dr_items, message = dr_manager.generate_dr_items(use_standard_codes=True)
    if dr_items is not None:
        print(f"✅ {message}")
        print(f"📝 生成的DR项目示例（前5个）:")
        for i, (_, row) in enumerate(dr_items.head(5).iterrows()):
            print(f"  {i+1}. {row['项目编码']} - {row['项目名称']}")
            if 'DR适用性' in row:
                print(f"     DR适用性: {row['DR适用性']} | 映射状态: {row.get('映射状态', 'N/A')}")
    else:
        print(f"❌ {message}")
        return
    
    # 5. 测试DR项目生成（原有编码）
    print(f"\n🔧 测试DR项目生成（原有编码）...")
    dr_items_legacy, message = dr_manager.generate_dr_items(use_standard_codes=False)
    if dr_items_legacy is not None:
        print(f"✅ {message}")
        print(f"📝 原有编码项目示例（前3个）:")
        for i, (_, row) in enumerate(dr_items_legacy.head(3).iterrows()):
            print(f"  {i+1}. {row['项目编码']} - {row['项目名称']}")
    else:
        print(f"❌ {message}")
    
    # 6. 测试差异报告导出
    print(f"\n📄 测试差异报告导出...")
    diff_report = dr_manager.export_difference_report()
    if diff_report is not None and not diff_report.empty:
        print(f"✅ 差异报告生成成功: {len(diff_report)}个问题")
        print(f"📋 问题类型分布:")
        problem_types = diff_report['问题类型'].value_counts()
        for problem_type, count in problem_types.items():
            print(f"  - {problem_type}: {count}个")
    else:
        print("ℹ️ 无差异问题需要报告")
    
    # 7. 测试综合报告导出
    print(f"\n📊 测试综合报告导出...")
    try:
        buffer = dr_manager.export_comprehensive_report()
        if buffer and hasattr(buffer, 'getvalue'):
            report_size = len(buffer.getvalue())
            print(f"✅ 综合报告生成成功: {report_size} bytes")
        else:
            print("❌ 综合报告生成失败")
    except Exception as e:
        print(f"❌ 综合报告生成异常: {str(e)}")
    
    print(f"\n🎉 DR处理架构测试完成！")

def test_dr_validator_standalone():
    """测试独立的DR映射验证器"""
    print(f"\n" + "="*60)
    print("测试独立DR映射验证器")
    print("="*60)
    
    # 创建验证器实例
    validator = DRMappingValidator()
    
    # 加载数据
    dr_df = pd.read_excel("data/DR项目结构-0705.xlsx", sheet_name='DR')
    success = validator.load_dr_data(dr_df)
    print(f"DR数据加载: {'✅ 成功' if success else '❌ 失败'}")
    
    # 加载标准字典
    processor = SimpleMedicalProcessor()
    with open("data/NEW_检查项目名称结构表 (9).xlsx", 'rb') as f:
        processor.load_data(f)
    three_level_dict = processor.generate_three_level_dict()
    
    success = validator.load_standard_dict(three_level_dict)
    print(f"标准字典加载: {'✅ 成功' if success else '❌ 失败'}")
    
    # 执行双向映射
    success, message = validator.perform_bidirectional_mapping()
    print(f"双向映射验证: {'✅' if success else '❌'} {message}")
    
    if success:
        # 获取统计信息
        stats = validator.get_mapping_statistics()
        print(f"📊 映射统计:")
        for category, stat_dict in stats.items():
            print(f"  {category}:")
            for key, value in stat_dict.items():
                print(f"    - {key}: {value}")
        
        # 导出差异报告
        diff_report = validator.export_difference_report()
        if not diff_report.empty:
            print(f"📄 差异报告: {len(diff_report)}个问题")
        else:
            print("ℹ️ 无差异问题")

def main():
    """主函数"""
    print("🔧 DR处理架构重构测试")
    print("测试目标:")
    print("1. 验证DR管理器的完整功能")
    print("2. 验证独立的DR映射验证器")
    print("3. 验证模块间的集成")
    print("4. 验证报告导出功能")
    
    try:
        test_dr_modules()
        test_dr_validator_standalone()
        
        print("\n" + "="*60)
        print("✅ 所有测试完成")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
