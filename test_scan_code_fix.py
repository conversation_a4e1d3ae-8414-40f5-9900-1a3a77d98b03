#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扫描方式编码修正功能
验证CTA、MRA、MRV等血管造影类扫描方式编码是否正确
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from streamlit_simple import SimpleMedicalProcessor

def test_scan_code_mapping():
    """测试扫描方式编码映射"""
    print("="*60)
    print("测试扫描方式编码映射功能")
    print("="*60)
    
    # 创建处理器实例
    processor = SimpleMedicalProcessor()
    
    # 加载数据
    data_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    with open(data_file, 'rb') as f:
        success, message = processor.load_data(f)
    
    if not success:
        print(f"❌ 数据加载失败: {message}")
        return
    
    print(f"✅ 数据加载成功: {message}")
    
    # 测试CT扫描方式映射
    print("\n" + "="*40)
    print("测试CT扫描方式编码映射")
    print("="*40)
    
    ct_mapping = processor.create_scan_mapping('CT')
    print(f"CT扫描方式映射数量: {len(ct_mapping)}")
    
    # 测试CTA编码
    test_cases_ct = [
        ('CTA', 'CT血管造影成像CTA'),
        ('血管成像CTA', 'CT血管造影成像CTA'),
        ('CT血管造影成像CTA', 'CT血管造影成像CTA')
    ]
    
    for test_name, expected_name in test_cases_ct:
        if test_name in ct_mapping:
            code = ct_mapping[test_name]
            print(f"✅ {test_name} -> 编码: {code}")
        else:
            # 使用find_scan_code方法测试
            code = processor.find_scan_code(test_name, ct_mapping, 'CT')
            print(f"🔍 {test_name} -> 编码: {code} (通过查找方法)")
    
    # 测试MR扫描方式映射
    print("\n" + "="*40)
    print("测试MR扫描方式编码映射")
    print("="*40)
    
    mr_mapping = processor.create_scan_mapping('MR')
    print(f"MR扫描方式映射数量: {len(mr_mapping)}")
    
    # 测试MRA、MRV编码
    test_cases_mr = [
        ('MRA', 'MR_血管平扫MRA'),
        ('MRV', 'MR_血管平扫MRV'),
        ('血管成像MRA', 'MR_血管平扫MRA'),
        ('血管成像MRV', 'MR_血管平扫MRV'),
        ('血管平扫MRA', 'MR_血管平扫MRA'),
        ('血管平扫MRV', 'MR_血管平扫MRV'),
        ('CE_MRA', 'MR-血管增强CE_MRA'),
        ('CE_MRV', 'MR-血管增强CE_MRV')
    ]
    
    for test_name, expected_name in test_cases_mr:
        if test_name in mr_mapping:
            code = mr_mapping[test_name]
            print(f"✅ {test_name} -> 编码: {code}")
        else:
            # 使用find_scan_code方法测试
            code = processor.find_scan_code(test_name, mr_mapping, 'MR')
            print(f"🔍 {test_name} -> 编码: {code} (通过查找方法)")

def test_no_part_items_integrity():
    """测试无部位项目字段完整性"""
    print("\n" + "="*60)
    print("测试无部位项目字段完整性")
    print("="*60)
    
    # 创建处理器实例
    processor = SimpleMedicalProcessor()
    
    # 加载数据
    data_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    with open(data_file, 'rb') as f:
        success, message = processor.load_data(f)
    
    if not success:
        print(f"❌ 数据加载失败: {message}")
        return
    
    # 生成CT无部位项目
    ct_no_part = processor.generate_no_part_items('CT')
    if ct_no_part is not None and len(ct_no_part) > 0:
        print(f"✅ CT无部位项目生成成功: {len(ct_no_part)}个")
        
        # 检查字段完整性
        required_fields = ['CT扫描分类编码', 'CT扫描分类名称', 'CT扫描编码', 'CT扫描名称']
        missing_fields = [field for field in required_fields if field not in ct_no_part.columns]
        
        if missing_fields:
            print(f"❌ CT无部位项目缺少字段: {missing_fields}")
        else:
            print("✅ CT无部位项目字段完整性检查通过")
            
        # 显示前几个CTA相关项目
        cta_items = ct_no_part[ct_no_part['项目名称'].str.contains('CTA', na=False)]
        if len(cta_items) > 0:
            print("\n📋 CTA相关无部位项目:")
            for _, item in cta_items.iterrows():
                print(f"  - {item['项目名称']} (编码: {item['项目编码']})")
                print(f"    扫描分类: {item['CT扫描分类名称']} (编码: {item['CT扫描分类编码']})")
    
    # 生成MR无部位项目
    mr_no_part = processor.generate_no_part_items('MR')
    if mr_no_part is not None and len(mr_no_part) > 0:
        print(f"\n✅ MR无部位项目生成成功: {len(mr_no_part)}个")
        
        # 检查字段完整性
        required_fields = ['MR成像分类编码', 'MR成像分类', 'MR成像编码', 'MR成像名称']
        missing_fields = [field for field in required_fields if field not in mr_no_part.columns]
        
        if missing_fields:
            print(f"❌ MR无部位项目缺少字段: {missing_fields}")
        else:
            print("✅ MR无部位项目字段完整性检查通过")
            
        # 显示前几个MRA、MRV相关项目
        mra_mrv_items = mr_no_part[mr_no_part['项目名称'].str.contains('MRA|MRV', na=False)]
        if len(mra_mrv_items) > 0:
            print("\n📋 MRA、MRV相关无部位项目:")
            for _, item in mra_mrv_items.iterrows():
                print(f"  - {item['项目名称']} (编码: {item['项目编码']})")
                print(f"    成像分类: {item['MR成像分类']} (编码: {item['MR成像分类编码']})")

def main():
    """主函数"""
    print("🔧 扫描方式编码修正功能测试")
    print("测试目标:")
    print("1. 验证CTA、MRA、MRV等血管造影类扫描方式保持原始编码")
    print("2. 验证扫描分类编码、扫描分类名称等字段完整性")
    
    try:
        test_scan_code_mapping()
        test_no_part_items_integrity()
        
        print("\n" + "="*60)
        print("✅ 所有测试完成")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
