import pandas as pd

# 读取Excel文件
xl = pd.ExcelFile('data/DR项目结构-0705.xlsx')
print('可用工作表:', xl.sheet_names)

# 读取矩阵数据（假设是第一个工作表）
df = pd.read_excel('data/DR项目结构-0705.xlsx', sheet_name=xl.sheet_names[0])
print('数据形状:', df.shape)
print('\n列名:')
print(df.columns.tolist())

# 问题项目列表
problem_items = ['骨盆左闭孔位DR', '骨盆右闭孔位DR', '右侧股骨髁间窝位DR', '左侧股骨髁间窝位DR']

# 获取方向列（排除项目名称和摆位列）
direction_cols = [col for col in df.columns if '位' in col and col not in ['项目名称', '摆位']]
print(f'\n方向列总数: {len(direction_cols)}')
print('方向列:', direction_cols)

for item in problem_items:
    row = df[df['项目名称'] == item]
    if not row.empty:
        print(f'\n=== {item} ===')
        
        # 检查激活的方向
        activated_directions = []
        for col in direction_cols:
            if row[col].iloc[0] == 1:
                activated_directions.append(col)
        
        print(f'激活的方向列: {activated_directions}')
        
        # 显示所有方向列的值
        direction_values = {}
        for col in direction_cols:
            direction_values[col] = row[col].iloc[0]
        
        print('所有方向列的值:')
        for col, val in direction_values.items():
            if val == 1:
                print(f'  {col}: {val} ✓')
            elif pd.isna(val) or val == 0:
                print(f'  {col}: {val}')
            else:
                print(f'  {col}: {val} ?')
    else:
        print(f'\n未找到项目: {item}')