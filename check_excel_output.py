#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel输出文件
"""

import pandas as pd

def check_excel_output():
    """
    检查生成的Excel文件
    """
    excel_file = '/Users/<USER>/Desktop/12-new/output/改进的DR编码系统结果_20250706_025743.xlsx'
    
    try:
        df = pd.read_excel(excel_file)
        
        print("=== Excel文件验证 ===")
        print(f"行数: {len(df)}")
        print(f"列数: {len(df.columns)}")
        
        print("\n列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. {col}")
        
        print("\n前5行数据:")
        print(df.head())
        
        print("\n摆位编码示例:")
        display_cols = ['项目名称', '体位名称', '体位编码', '方向名称', '方向编码', '摆位编码']
        print(df[display_cols].head(10))
        
        print("\n数据类型:")
        print(df.dtypes)
        
        print("\n=== Excel文件检查完成 ===")
        
    except Exception as e:
        print(f"检查Excel文件时出错: {e}")

if __name__ == '__main__':
    check_excel_output()