#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试扫描方式字典字段完整性
验证是否保留了原始sheet表中的所有字段
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from streamlit_simple import SimpleMedicalProcessor

def test_scan_dict_fields():
    """测试扫描方式字典字段完整性"""
    print("="*60)
    print("测试扫描方式字典字段完整性")
    print("="*60)
    
    # 创建处理器实例
    processor = SimpleMedicalProcessor()
    
    # 加载数据
    data_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    with open(data_file, 'rb') as f:
        success, message = processor.load_data(f)
    
    if not success:
        print(f"❌ 数据加载失败: {message}")
        return
    
    print(f"✅ 数据加载成功: {message}")
    
    # 检查原始CT扫描方式数据
    print("\n" + "="*40)
    print("原始CT扫描方式数据")
    print("="*40)
    print(f"原始CT扫描方式数据行数: {len(processor.ct_scan_df)}")
    print(f"原始CT扫描方式数据列数: {len(processor.ct_scan_df.columns)}")
    print("原始CT扫描方式字段:", list(processor.ct_scan_df.columns))
    
    # 生成CT扫描方式字典
    ct_dict = processor.generate_scan_dict('CT')
    if ct_dict is not None:
        print(f"\n生成的CT扫描方式字典行数: {len(ct_dict)}")
        print(f"生成的CT扫描方式字典列数: {len(ct_dict.columns)}")
        print("生成的CT扫描方式字段:", list(ct_dict.columns))
        
        # 检查字段是否完整保留
        original_fields = set(processor.ct_scan_df.columns)
        generated_fields = set(ct_dict.columns)
        
        missing_fields = original_fields - generated_fields
        new_fields = generated_fields - original_fields
        
        if missing_fields:
            print(f"❌ 缺失的字段: {missing_fields}")
        else:
            print("✅ 所有原始字段都已保留")
            
        if new_fields:
            print(f"✅ 新增的字段: {new_fields}")
        
        # 显示CTA相关数据
        print("\n📋 CTA相关数据:")
        cta_data = ct_dict[ct_dict['CT扫描名称'].str.contains('CTA', na=False)]
        if len(cta_data) > 0:
            for _, row in cta_data.iterrows():
                print(f"  - {row['CT扫描名称']} (编码: {row['CT扫描编码']})")
                print(f"    分类: {row['CT扫描分类名称']} (分类编码: {row['CT扫描分类编码']})")
                print(f"    清理后名称: {row['清理后名称']}")
    
    # 检查原始MR扫描方式数据
    print("\n" + "="*40)
    print("原始MR扫描方式数据")
    print("="*40)
    print(f"原始MR扫描方式数据行数: {len(processor.mr_scan_df)}")
    print(f"原始MR扫描方式数据列数: {len(processor.mr_scan_df.columns)}")
    print("原始MR扫描方式字段:", list(processor.mr_scan_df.columns))
    
    # 生成MR扫描方式字典
    mr_dict = processor.generate_scan_dict('MR')
    if mr_dict is not None:
        print(f"\n生成的MR扫描方式字典行数: {len(mr_dict)}")
        print(f"生成的MR扫描方式字典列数: {len(mr_dict.columns)}")
        print("生成的MR扫描方式字段:", list(mr_dict.columns))
        
        # 检查字段是否完整保留
        original_fields = set(processor.mr_scan_df.columns)
        generated_fields = set(mr_dict.columns)
        
        missing_fields = original_fields - generated_fields
        new_fields = generated_fields - original_fields
        
        if missing_fields:
            print(f"❌ 缺失的字段: {missing_fields}")
        else:
            print("✅ 所有原始字段都已保留")
            
        if new_fields:
            print(f"✅ 新增的字段: {new_fields}")
        
        # 显示MRA、MRV相关数据
        print("\n📋 MRA、MRV相关数据:")
        mra_mrv_data = mr_dict[mr_dict['MR成像名称'].str.contains('MRA|MRV', na=False)]
        if len(mra_mrv_data) > 0:
            for _, row in mra_mrv_data.iterrows():
                print(f"  - {row['MR成像名称']} (编码: {row['MR成像编码']})")
                print(f"    分类: {row['MR成像分类']} (分类编码: {row['MR成像分类编码']})")
                print(f"    清理后名称: {row['清理后名称']}")
                if pd.notna(row.get('父记录')):
                    print(f"    父记录: {row['父记录']}")

def test_demo_script():
    """测试demo_script的扫描方式字典生成"""
    print("\n" + "="*60)
    print("测试demo_script的扫描方式字典生成")
    print("="*60)
    
    # 导入demo_script
    from demo_script import MedicalProcessor
    
    # 创建处理器实例
    processor = MedicalProcessor()
    
    # 加载数据
    data_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    success, message = processor.load_data(data_file)
    
    if not success:
        print(f"❌ 数据加载失败: {message}")
        return
    
    print(f"✅ 数据加载成功")
    
    # 生成CT扫描方式字典
    print("\n--- CT扫描方式字典 ---")
    ct_dict = processor.generate_scan_dict('CT')
    if ct_dict is not None:
        print(f"字段数量: {len(ct_dict.columns)}")
        print("字段列表:", list(ct_dict.columns))
    
    # 生成MR扫描方式字典
    print("\n--- MR扫描方式字典 ---")
    mr_dict = processor.generate_scan_dict('MR')
    if mr_dict is not None:
        print(f"字段数量: {len(mr_dict.columns)}")
        print("字段列表:", list(mr_dict.columns))

def main():
    """主函数"""
    print("🔧 扫描方式字典字段完整性测试")
    print("测试目标:")
    print("1. 验证扫描方式字典保留了原始sheet表中的所有字段")
    print("2. 验证新增的清理后名称字段")
    print("3. 验证血管造影类扫描方式的字段完整性")
    
    try:
        test_scan_dict_fields()
        test_demo_script()
        
        print("\n" + "="*60)
        print("✅ 所有测试完成")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
