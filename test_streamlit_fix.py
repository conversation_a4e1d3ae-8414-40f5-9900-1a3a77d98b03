#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Streamlit应用修复
验证列名错误是否已修复
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from streamlit_simple import SimpleMedicalProcessor

def test_streamlit_columns():
    """测试Streamlit应用中的列名问题"""
    print("="*60)
    print("测试Streamlit应用列名修复")
    print("="*60)
    
    # 创建处理器实例
    processor = SimpleMedicalProcessor()
    
    # 加载数据
    data_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    if not os.path.exists(data_file):
        print(f"❌ 数据文件不存在: {data_file}")
        return
    
    with open(data_file, 'rb') as f:
        success, message = processor.load_data(f)
    
    if not success:
        print(f"❌ 数据加载失败: {message}")
        return
    
    print(f"✅ 数据加载成功")
    
    # 测试CT扫描方式字典
    print("\n--- 测试CT扫描方式字典 ---")
    ct_dict = processor.generate_scan_dict('CT')
    if ct_dict is not None:
        print(f"CT扫描方式字典列数: {len(ct_dict.columns)}")
        print("CT扫描方式字典列名:", list(ct_dict.columns))
        
        # 测试Streamlit中使用的列名
        try:
            comparison_cols = ['CT扫描名称', '清理后名称']
            test_df = ct_dict[comparison_cols]
            print("✅ CT列名测试通过")
            print(f"对比列数据形状: {test_df.shape}")
        except KeyError as e:
            print(f"❌ CT列名测试失败: {e}")
    
    # 测试MR扫描方式字典
    print("\n--- 测试MR扫描方式字典 ---")
    mr_dict = processor.generate_scan_dict('MR')
    if mr_dict is not None:
        print(f"MR扫描方式字典列数: {len(mr_dict.columns)}")
        print("MR扫描方式字典列名:", list(mr_dict.columns))
        
        # 测试Streamlit中使用的列名
        try:
            comparison_cols = ['MR成像名称', '清理后名称']
            test_df = mr_dict[comparison_cols]
            print("✅ MR列名测试通过")
            print(f"对比列数据形状: {test_df.shape}")
        except KeyError as e:
            print(f"❌ MR列名测试失败: {e}")

def main():
    """主函数"""
    print("🔧 Streamlit应用列名修复测试")
    print("测试目标: 验证扫描方式字典的列名是否正确")
    
    try:
        test_streamlit_columns()
        
        print("\n" + "="*60)
        print("✅ 测试完成")
        print("如果所有测试都通过，Streamlit应用应该可以正常运行")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
