import pandas as pd
import json
from collections import defaultdict

def load_encoding_tables():
    """从Excel文件中加载方向和体位编码表"""
    excel_file = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx'
    
    # 读取方向编码表
    direction_df = pd.read_excel(excel_file, sheet_name='方向')
    direction_mapping = dict(zip(direction_df['方向'], direction_df['方向编码']))
    
    # 读取体位编码表
    position_df = pd.read_excel(excel_file, sheet_name='体位')
    position_mapping = dict(zip(position_df['体位'], position_df['体位编码']))
    
    return direction_mapping, position_mapping

def find_best_match(text, mapping_dict):
    """在文本中查找最佳匹配的编码"""
    if not text or pd.isna(text):
        return 'UNK', []
    
    text = str(text)
    matched_keywords = []
    
    # 按关键词长度降序排列，优先匹配长关键词
    sorted_keywords = sorted(mapping_dict.keys(), key=len, reverse=True)
    
    for keyword in sorted_keywords:
        if keyword in text:
            matched_keywords.append(keyword)
            return mapping_dict[keyword], matched_keywords
    
    return 'UNK', matched_keywords

def generate_position_encoding(excel_file_path, sheet_name, direction_mapping, position_mapping):
    """生成摆位编码表"""
    # 读取主数据
    df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
    
    # 确保必要的列存在
    required_columns = ['二级部位', '三级部位', '检查项目', '摆位']
    available_columns = list(df.columns)
    print(f"可用列: {available_columns}")
    
    # 检查列是否存在，如果不存在则尝试使用其他列名
    column_mapping = {}
    
    # 定义列名映射规则
    column_alternatives = {
        '检查项目': ['项目名称', '检查项目', '项目'],
        '二级部位': ['二级部位'],
        '三级部位': ['三级部位'],
        '摆位': ['摆位']
    }
    
    for col in required_columns:
        found = False
        # 首先检查精确匹配
        if col in df.columns:
            column_mapping[col] = col
            found = True
        else:
            # 检查预定义的替代列名
            if col in column_alternatives:
                for alt_col in column_alternatives[col]:
                    if alt_col in df.columns:
                        column_mapping[col] = alt_col
                        found = True
                        print(f"使用 '{alt_col}' 替代 '{col}'")
                        break
            
            # 如果还没找到，尝试模糊匹配
            if not found:
                for available_col in available_columns:
                    if col in available_col or available_col in col:
                        column_mapping[col] = available_col
                        found = True
                        print(f"使用 '{available_col}' 替代 '{col}'")
                        break
        
        if not found:
            print(f"警告: 缺少列 '{col}' 且找不到替代列")
    
    if len(column_mapping) < len(required_columns):
        print("缺少必要的列，无法继续处理")
        return None, None
    
    results = []
    stats = {
        '摆位编码': defaultdict(int),
        '体位编码': defaultdict(int),
        '方向编码': defaultdict(int)
    }
    
    for index, row in df.iterrows():
        # 获取基本信息
        二级部位 = row.get(column_mapping.get('二级部位', ''), '')
        三级部位 = row.get(column_mapping.get('三级部位', ''), '')
        检查项目 = row.get(column_mapping.get('检查项目', ''), '')
        摆位 = row.get(column_mapping.get('摆位', ''), '')
        
        # 查找体位编码
        体位编码, 体位关键词 = find_best_match(摆位, position_mapping)
        
        # 查找方向编码
        方向编码, 方向关键词 = find_best_match(摆位, direction_mapping)
        
        # 生成摆位编码（体位编码_方向编码）
        摆位编码 = f"{体位编码}_{方向编码}"
        
        # 统计
        stats['摆位编码'][摆位编码] += 1
        stats['体位编码'][体位编码] += 1
        stats['方向编码'][方向编码] += 1
        
        # 保存结果
        result = {
            '二级部位': 二级部位,
            '三级部位': 三级部位,
            '检查项目': 检查项目,
            '摆位': 摆位,
            '体位编码': 体位编码,
            '方向编码': 方向编码,
            '摆位编码': 摆位编码,
            '匹配的体位关键词': 体位关键词,
            '匹配的方向关键词': 方向关键词
        }
        results.append(result)
    
    return results, stats

def main():
    excel_file = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx'
    
    # 获取所有工作表名称
    print("检查Excel文件中的工作表...")
    xl = pd.ExcelFile(excel_file)
    sheet_names = xl.sheet_names
    print(f"可用工作表: {sheet_names}")
    
    # 选择主数据工作表
    main_sheet = None
    for sheet in sheet_names:
        if sheet not in ['方向', '体位']:
            main_sheet = sheet
            break
    
    if main_sheet is None:
        print("错误: 找不到主数据工作表")
        return
    
    print(f"使用 '{main_sheet}' 作为主数据工作表")
    
    # 加载编码表
    print("\n正在加载方向和体位编码表...")
    direction_mapping, position_mapping = load_encoding_tables()
    
    print(f"方向编码表: {len(direction_mapping)} 项")
    print("方向编码映射:")
    for direction, code in sorted(direction_mapping.items()):
        print(f"  {direction} -> {code}")
    
    print(f"\n体位编码表: {len(position_mapping)} 项")
    print("体位编码映射:")
    for position, code in sorted(position_mapping.items()):
        print(f"  {position} -> {code}")
    
    # 生成摆位编码
    print("\n正在生成摆位编码...")
    results, stats = generate_position_encoding(excel_file, main_sheet, direction_mapping, position_mapping)
    
    if results is None:
        print("生成失败！")
        return
    
    # 保存为JSON文件
    json_output = '摆位编码表_真实数据生成.json'
    with open(json_output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    # 保存为Excel文件
    excel_output = '摆位编码表_真实数据生成.xlsx'
    df_output = pd.DataFrame(results)
    df_output.to_excel(excel_output, index=False)
    
    # 输出统计信息
    print(f"\n生成完成！共处理 {len(results)} 条记录")
    print(f"JSON文件: {json_output}")
    print(f"Excel文件: {excel_output}")
    
    print("\n=== 编码统计 ===")
    print("摆位编码分布:")
    for code, count in sorted(stats['摆位编码'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {code}: {count} 次")
    
    print("\n体位编码分布:")
    for code, count in sorted(stats['体位编码'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {code}: {count} 次")
    
    print("\n方向编码分布:")
    for code, count in sorted(stats['方向编码'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {code}: {count} 次")

if __name__ == "__main__":
    main()