#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化组件修复
"""

import pandas as pd
import sys
import os

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from dr_manager import DRManager
from streamlit_simple import SimpleMedicalProcessor

def test_visualization_fix():
    """测试可视化组件修复"""
    print("="*60)
    print("测试可视化组件修复")
    print("="*60)
    
    # 创建DR管理器
    dr_manager = DRManager()
    
    # 加载数据
    dr_file = "data/DR项目结构-0705.xlsx"
    success, message = dr_manager.load_dr_data(dr_file)
    print(f"DR数据加载: {'✅' if success else '❌'} {message}")
    
    # 加载标准字典
    processor = SimpleMedicalProcessor()
    ct_mr_file = "data/NEW_检查项目名称结构表 (9).xlsx"
    with open(ct_mr_file, 'rb') as f:
        processor.load_data(f)
    
    three_level_dict = processor.generate_three_level_dict()
    success, message = dr_manager.set_standard_dict(three_level_dict)
    print(f"标准字典设置: {'✅' if success else '❌'} {message}")
    
    # 执行映射验证
    success, message = dr_manager.validate_part_mapping()
    print(f"映射验证: {'✅' if success else '❌'} {message}")
    
    # 获取差异详情
    diff_details = dr_manager.get_difference_details()
    
    # 检查数据结构
    print(f"\n📋 差异详情数据结构检查:")
    for key, df in diff_details.items():
        print(f"  {key}: {len(df)}行")
        if len(df) > 0:
            print(f"    列名: {list(df.columns)}")
            # 检查是否有问题列
            if 'DR项目数量' in df.columns:
                print(f"    ❌ 发现问题列: DR项目数量")
            else:
                print(f"    ✅ 列名正常")
    
    # 测试可视化组件
    print(f"\n🔧 测试可视化组件...")
    try:
        from dr_visualization import DRVisualization
        
        # 模拟Streamlit环境（不实际显示）
        class MockStreamlit:
            @staticmethod
            def info(text): print(f"INFO: {text}")
            @staticmethod
            def warning(text): print(f"WARNING: {text}")
            @staticmethod
            def error(text): print(f"ERROR: {text}")
            @staticmethod
            def dataframe(df, **kwargs): 
                print(f"DATAFRAME: {len(df)}行 x {len(df.columns)}列")
                print(f"列名: {list(df.columns)}")
            @staticmethod
            def tabs(names): return [MockTab() for _ in names]
            @staticmethod
            def columns(n): return [MockCol() for _ in range(n)]
            @staticmethod
            def expander(text, **kwargs): return MockExpander()
            @staticmethod
            def selectbox(label, options): return options[0] if options else None
            @staticmethod
            def write(text): print(f"WRITE: {text}")
        
        class MockTab:
            def __enter__(self): return self
            def __exit__(self, *args): pass
        
        class MockCol:
            def __enter__(self): return self
            def __exit__(self, *args): pass
        
        class MockExpander:
            def __enter__(self): return self
            def __exit__(self, *args): pass
        
        # 替换streamlit模块
        import sys
        sys.modules['streamlit'] = MockStreamlit()
        
        # 测试差异详情显示
        print(f"测试差异详情显示...")
        DRVisualization.display_difference_details(diff_details)
        print(f"✅ 差异详情显示测试通过")
        
    except Exception as e:
        print(f"❌ 可视化组件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    try:
        test_visualization_fix()
        print(f"\n✅ 可视化组件修复测试完成")
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
