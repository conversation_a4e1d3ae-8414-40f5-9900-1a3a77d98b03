import pandas as pd
import json
from collections import defaultdict

def analyze_dr_matrix_structure():
    """分析DR表格的矩阵编码结构"""
    excel_file = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx'
    
    # 获取所有工作表
    xl = pd.ExcelFile(excel_file)
    print(f"所有工作表: {xl.sheet_names}")
    
    # 分析主数据工作表（非方向、体位工作表）
    main_sheet = None
    for sheet in xl.sheet_names:
        if sheet not in ['方向', '体位']:
            main_sheet = sheet
            break
    
    if main_sheet:
        print(f"\n分析主数据工作表: {main_sheet}")
        df = pd.read_excel(excel_file, sheet_name=main_sheet)
        
        print(f"数据形状: {df.shape}")
        print(f"列数: {len(df.columns)}")
        
        # 识别体位和方向列
        position_columns = []
        direction_columns = []
        
        # 用户提供的体位列表
        position_list = ['仰卧位', '俯卧位', '左侧卧', '右侧卧', '斜位', '左前斜位', '右前斜位', 
                        '左后斜位', '右后斜位', '站立位', '倒立位', '内斜位', '外斜位', '外展位', 
                        '内收位', '过伸位', '过屈位', '张口位', '闭口位', '前弓位', '蛙形位', 
                        '全景', '应力位', '没有']
        
        # 用户提供的方向列表
        direction_list = ['前后位', '后前位', '轴位', '前后轴位', '后前轴位', '切线位', 
                         '左前后斜位', '右前后斜位', '右后前斜位', '左后前斜位', '左侧位', 
                         '右侧位', '侧位', '穿胸位', '乳腺摄影', '头尾位', '内外斜位', 
                         '内外位', '外内位', '尾头位', '左弯曲位', '右弯曲位', '轴斜位', '没有']
        
        # 识别体位列
        for col in df.columns:
            if col in position_list:
                position_columns.append(col)
        
        # 识别方向列
        for col in df.columns:
            if col in direction_list:
                direction_columns.append(col)
        
        print(f"\n识别到的体位列 ({len(position_columns)} 个):")
        for i, col in enumerate(position_columns):
            print(f"{i+1:2d}. {col}")
        
        print(f"\n识别到的方向列 ({len(direction_columns)} 个):")
        for i, col in enumerate(direction_columns):
            print(f"{i+1:2d}. {col}")
        
        # 分析前几行数据的编码模式
        print("\n分析前5行的编码模式:")
        basic_columns = ['一级部位', '二级部位', '三级部位', '项目名称', '摆位']
        available_basic_columns = [col for col in basic_columns if col in df.columns]
        
        for idx in range(min(5, len(df))):
            row = df.iloc[idx]
            print(f"\n第{idx+1}行:")
            
            # 显示基本信息
            for col in available_basic_columns:
                print(f"  {col}: {row[col]}")
            
            # 显示体位编码（值为1的列）
            active_positions = [col for col in position_columns if row[col] == 1]
            print(f"  激活的体位: {active_positions}")
            
            # 显示方向编码（值为1的列）
            active_directions = [col for col in direction_columns if row[col] == 1]
            print(f"  激活的方向: {active_directions}")
        
        return df, position_columns, direction_columns, available_basic_columns
    
    return None, [], [], []

def generate_matrix_based_encoding_with_none():
    """基于矩阵结构生成摆位编码，使用'没有'处理缺失数据"""
    df, position_columns, direction_columns, basic_columns = analyze_dr_matrix_structure()
    
    if df is None:
        print("无法分析数据结构")
        return
    
    # 加载方向和体位编码表
    excel_file = '/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx'
    
    # 读取方向编码表
    direction_df = pd.read_excel(excel_file, sheet_name='方向')
    direction_code_mapping = dict(zip(direction_df['方向'], direction_df['方向编码']))
    
    # 读取体位编码表
    position_df = pd.read_excel(excel_file, sheet_name='体位')
    position_code_mapping = dict(zip(position_df['体位'], position_df['体位编码']))
    
    # 确保'没有'在编码映射中
    if '没有' not in position_code_mapping:
        # 查找'没有'对应的编码，如果没有则使用默认值
        none_position_code = None
        for pos, code in position_code_mapping.items():
            if '没有' in str(pos) or 'none' in str(pos).lower():
                none_position_code = code
                break
        if none_position_code is None:
            none_position_code = '99'  # 默认编码
        position_code_mapping['没有'] = none_position_code
        print(f"添加体位编码映射: 没有 -> {none_position_code}")
    
    if '没有' not in direction_code_mapping:
        # 查找'没有'对应的编码，如果没有则使用默认值
        none_direction_code = None
        for dir_name, code in direction_code_mapping.items():
            if '没有' in str(dir_name) or 'none' in str(dir_name).lower():
                none_direction_code = code
                break
        if none_direction_code is None:
            none_direction_code = '99'  # 默认编码
        direction_code_mapping['没有'] = none_direction_code
        print(f"添加方向编码映射: 没有 -> {none_direction_code}")
    
    # 添加缺失的体位映射（根据实际数据补充）
    if '仰卧位' not in position_code_mapping:
        position_code_mapping['仰卧位'] = '0'
        print("警告：体位编码表中未找到'仰卧位'，已添加默认映射 仰卧位 -> 0")
    
    if '俯卧位' not in position_code_mapping:
        position_code_mapping['俯卧位'] = '1'
        print("警告：体位编码表中未找到'俯卧位'，已添加默认映射 俯卧位 -> 1")
    
    # 查找项目名称列
    project_column = None
    for col in df.columns:
        if '项目' in str(col) and ('名称' in str(col) or col == '项目'):
            project_column = col
            break
    
    if project_column is None:
        print("警告：未找到项目名称列")
        return
    
    print(f"使用项目名称列: {project_column}")
    
    # 过滤掉项目名称为空的记录
    original_count = len(df)
    df = df.dropna(subset=[project_column])
    df = df[df[project_column].str.strip() != '']
    filtered_count = len(df)
    print(f"过滤前记录数: {original_count}")
    print(f"过滤后记录数: {filtered_count}")
    print(f"已移除 {original_count - filtered_count} 条项目名称为空的记录")
    
    print(f"\n方向编码映射: {direction_code_mapping}")
    print(f"体位编码映射: {position_code_mapping}")
    
    results = []
    stats = {
        '摆位编码': defaultdict(int),
        '体位编码': defaultdict(int),
        '方向编码': defaultdict(int)
    }
    
    for idx, row in df.iterrows():
        # 获取基本信息
        basic_info = {}
        for col in basic_columns:
            basic_info[col] = row.get(col, '')
        
        # 找到激活的体位（值为1的体位列）
        active_positions = [col for col in position_columns if row[col] == 1]
        
        # 找到激活的方向（值为1的方向列）
        active_directions = [col for col in direction_columns if row[col] == 1]
        
        # 获取体位编码 - 如果没有激活的体位，使用'没有'
        if active_positions:
            position_name = active_positions[0]  # 假设只有一个激活的体位
            体位编码 = position_code_mapping.get(position_name, position_code_mapping['没有'])
        else:
            position_name = '没有'
            体位编码 = position_code_mapping['没有']
        
        # 获取方向编码 - 如果没有激活的方向，使用'没有'
        if active_directions:
            direction_name = active_directions[0]  # 假设只有一个激活的方向
            方向编码 = direction_code_mapping.get(direction_name, direction_code_mapping['没有'])
        else:
            direction_name = '没有'
            方向编码 = direction_code_mapping['没有']
        
        # 生成摆位编码
        摆位编码 = f"{体位编码}_{方向编码}"
        
        # 统计
        stats['摆位编码'][摆位编码] += 1
        stats['体位编码'][体位编码] += 1
        stats['方向编码'][方向编码] += 1
        
        # 保存结果
        result = basic_info.copy()
        result.update({
            '体位编码': 体位编码,
            '方向编码': 方向编码,
            '摆位编码': 摆位编码,
            '激活的体位': active_positions,
            '激活的方向': active_directions,
            '体位名称': position_name,
            '方向名称': direction_name
        })
        results.append(result)
    
    # 保存结果
    json_output = '摆位编码表_使用没有处理缺失数据.json'
    with open(json_output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    excel_output = '摆位编码表_使用没有处理缺失数据.xlsx'
    df_output = pd.DataFrame(results)
    df_output.to_excel(excel_output, index=False)
    
    # 输出统计信息
    print(f"\n生成完成！共处理 {len(results)} 条记录")
    print(f"JSON文件: {json_output}")
    print(f"Excel文件: {excel_output}")
    
    # 收集使用'没有'编码的项目信息
    none_projects = []
    for result in results:
        pos_code = result['摆位编码']
        body_code = result['体位编码']
        dir_code = result['方向编码']
        project_name = result.get('项目名称', '')
        position_name = result.get('摆位', '')
        body_name = result.get('体位名称', '')
        dir_name = result.get('方向名称', '')
        
        # 检查是否使用了'没有'编码
        none_position_code = position_code_mapping['没有']
        none_direction_code = direction_code_mapping['没有']
        
        if body_code == none_position_code or dir_code == none_direction_code:
            none_projects.append({
                '项目名称': project_name,
                '摆位': position_name,
                '摆位编码': pos_code,
                '体位编码': body_code,
                '方向编码': dir_code,
                '体位名称': body_name,
                '方向名称': dir_name
            })
    
    print("\n=== 编码统计 ===") 
    print("摆位编码分布:")
    for code, count in sorted(stats['摆位编码'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {code}: {count} 次")
    
    print("\n体位编码分布:")
    for code, count in sorted(stats['体位编码'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {code}: {count} 次")
    
    print("\n方向编码分布:")
    for code, count in sorted(stats['方向编码'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {code}: {count} 次")
    
    # 显示使用'没有'编码的项目详情
    print(f"\n=== 使用'没有'编码的项目和摆位 ===\n")
    print(f"总计 {len(none_projects)} 个项目/摆位使用了'没有'编码:")
    for i, none_item in enumerate(none_projects, 1):
        print(f"{i}. 项目: {none_item['项目名称']} | 摆位: {none_item['摆位']} | 摆位编码: {none_item['摆位编码']} | 体位: {none_item['体位名称']} | 方向: {none_item['方向名称']}")
    
    print(f"\n=== 完成全部摆位编码 ===\n")
    print(f"✅ 所有 {len(results)} 个项目都已完成编码")
    print(f"✅ 缺失数据已使用'没有'编码处理")
    print(f"✅ 无UNK编码，所有摆位都有明确的编码")

if __name__ == "__main__":
    print("=== DR表格矩阵编码分析（使用'没有'处理缺失数据）===")
    generate_matrix_based_encoding_with_none()