import pandas as pd
import numpy as np

def check_specific_items():
    """检查特定问题项目在Excel中的数据状态"""
    
    # 读取Excel文件
    try:
        # 获取所有工作表名称
        excel_file = pd.ExcelFile('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx')
        print(f"工作表列表: {excel_file.sheet_names}")
        
        # 读取DR工作表
        df = pd.read_excel('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx', sheet_name='DR')
        print(f"\n数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 问题项目列表
        problem_items = [
            "骨盆左闭孔位DR",
            "骨盆右闭孔位DR", 
            "右侧股骨髁间窝位DR",
            "左侧股骨髁间窝位DR"
        ]
        
        print("\n=== 检查问题项目的详细信息 ===")
        
        for item in problem_items:
            print(f"\n--- 检查项目: {item} ---")
            
            # 查找该项目
            item_rows = df[df['项目名称'] == item]
            
            if item_rows.empty:
                print(f"❌ 未找到项目: {item}")
                continue
                
            print(f"✅ 找到项目，共 {len(item_rows)} 行")
            
            for idx, row in item_rows.iterrows():
                print(f"\n  行索引: {idx}")
                print(f"  项目名称: {row['项目名称']}")
                
                # 检查体位列
                position_cols = [col for col in df.columns if '体位' in col or col in ['仰卧位', '俯卧位', '侧卧位', '坐位', '立位']]
                print(f"\n  体位相关列及其值:")
                for col in position_cols:
                    if col in df.columns:
                        val = row[col]
                        if pd.notna(val) and val != 0:
                            print(f"    {col}: {val} ✅")
                        else:
                            print(f"    {col}: {val}")
                
                # 检查方向列
                direction_cols = [col for col in df.columns if any(x in col for x in ['位', '前后', '侧', '轴', '斜'])]
                direction_cols = [col for col in direction_cols if '体位' not in col and '项目' not in col]
                
                print(f"\n  方向相关列及其值:")
                active_directions = []
                for col in direction_cols:
                    if col in df.columns:
                        val = row[col]
                        if pd.notna(val) and val == 1:
                            active_directions.append(col)
                            print(f"    {col}: {val} ✅")
                        elif pd.notna(val) and val != 0:
                            print(f"    {col}: {val}")
                
                if active_directions:
                    print(f"\n  ✅ 激活的方向列: {active_directions}")
                else:
                    print(f"\n  ❌ 没有激活的方向列 (所有方向列都是空值或0)")
                    
                    # 显示前10个方向列的值作为示例
                    print(f"\n  前10个方向列的值示例:")
                    for i, col in enumerate(direction_cols[:10]):
                        if col in df.columns:
                            val = row[col]
                            print(f"    {col}: {val}")
        
        print("\n=== 检查完成 ===")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_specific_items()