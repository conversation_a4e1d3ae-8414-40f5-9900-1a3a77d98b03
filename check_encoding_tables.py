import pandas as pd

# 读取体位编码表
try:
    df_pos = pd.read_excel('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx', sheet_name='体位')
    print('体位编码表:')
    print(df_pos.head(10))
    print(f'体位编码表形状: {df_pos.shape}')
except Exception as e:
    print(f'读取体位编码表失败: {e}')

# 读取方向编码表
try:
    df_dir = pd.read_excel('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx', sheet_name='方向')
    print('\n方向编码表:')
    print(df_dir.head(10))
    print(f'方向编码表形状: {df_dir.shape}')
except Exception as e:
    print(f'读取方向编码表失败: {e}')

# 检查工作表名称
try:
    xl_file = pd.ExcelFile('/Users/<USER>/Desktop/12-new/data/DR项目结构-0705.xlsx')
    print(f'\n所有工作表名称: {xl_file.sheet_names}')
except Exception as e:
    print(f'读取工作表名称失败: {e}')